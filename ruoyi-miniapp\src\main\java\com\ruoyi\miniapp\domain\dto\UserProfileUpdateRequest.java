package com.ruoyi.miniapp.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户完善资料请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@ApiModel("用户完善资料请求")
public class UserProfileUpdateRequest
{
    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("微信头像")
    private String avatar;

    @ApiModelProperty("姓名")
    private String realName;


    @ApiModelProperty("性别（0男 1女 2未知）")
    private String sex;

    @ApiModelProperty("出生日期（格式：yyyy-MM-dd）")
    private String birthDate;

    @ApiModelProperty("籍贯")
    private String region;

    @ApiModelProperty("联系方式")
    private String phonenumber;

    @ApiModelProperty("形象照")
    private String portraitUrl;

    @ApiModelProperty("毕业院校")
    private String graduateSchool;

    @ApiModelProperty("毕业年份")
    private String graduationYear;

    @ApiModelProperty("专业")
    private String major;

    @ApiModelProperty("学院")
    private String college;

    @ApiModelProperty("当前公司")
    private String currentCompany;

    @ApiModelProperty("行业领域（多个ID用逗号分隔）")
    private String industryField;

    @ApiModelProperty("职位标题")
    private String positionTitle;

    @ApiModelProperty("个人介绍")
    private String personalIntroduction;

    @ApiModelProperty("资料完成度百分比(0-100)")
    private Integer profileCompletionRate;

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getPortraitUrl() {
        return portraitUrl;
    }

    public void setPortraitUrl(String portraitUrl) {
        this.portraitUrl = portraitUrl;
    }

    public String getGraduateSchool() {
        return graduateSchool;
    }

    public void setGraduateSchool(String graduateSchool) {
        this.graduateSchool = graduateSchool;
    }

    public String getGraduationYear() {
        return graduationYear;
    }

    public void setGraduationYear(String graduationYear) {
        this.graduationYear = graduationYear;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getCollege() {
        return college;
    }

    public void setCollege(String college) {
        this.college = college;
    }

    public String getCurrentCompany() {
        return currentCompany;
    }

    public void setCurrentCompany(String currentCompany) {
        this.currentCompany = currentCompany;
    }

    public String getIndustryField() {
        return industryField;
    }

    public void setIndustryField(String industryField) {
        this.industryField = industryField;
    }

    public String getPositionTitle() {
        return positionTitle;
    }

    public void setPositionTitle(String positionTitle) {
        this.positionTitle = positionTitle;
    }

    public String getPersonalIntroduction() {
        return personalIntroduction;
    }

    public void setPersonalIntroduction(String personalIntroduction) {
        this.personalIntroduction = personalIntroduction;
    }

    public Integer getProfileCompletionRate() {
        return profileCompletionRate;
    }

    public void setProfileCompletionRate(Integer profileCompletionRate) {
        this.profileCompletionRate = profileCompletionRate;
    }

    @Override
    public String toString() {
        return "UserProfileUpdateRequest{" +
                "nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", realName='" + realName + '\'' +
                ", sex='" + sex + '\'' +
                ", birthDate='" + birthDate + '\'' +
                ", region='" + region + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                ", portraitUrl='" + portraitUrl + '\'' +
                ", graduateSchool='" + graduateSchool + '\'' +
                ", graduationYear='" + graduationYear + '\'' +
                ", major='" + major + '\'' +
                ", college='" + college + '\'' +
                ", currentCompany='" + currentCompany + '\'' +
                ", industryField='" + industryField + '\'' +
                ", positionTitle='" + positionTitle + '\'' +
                ", personalIntroduction='" + personalIntroduction + '\'' +
                ", profileCompletionRate=" + profileCompletionRate +
                '}';
    }
}
