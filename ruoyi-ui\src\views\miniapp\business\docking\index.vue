<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="需求标题" prop="demandTitle">
        <el-input
          v-model="queryParams.demandTitle"
          placeholder="请输入需求标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对接用户" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对接状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择对接状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="已取消" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="对接时间" prop="dockingTime">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:docking:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dockingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="dockingId" width="70" />

      <!-- 需求信息 -->
      <el-table-column label="需求标题" align="left" min-width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <div style="font-weight: bold; color: #303133;">
              {{ scope.row.demandTitle || '未知需求' }}
            </div>
            <div style="color: #909399; font-size: 12px;">
              ID: {{ scope.row.demandId }}
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 对接用户信息 -->
      <el-table-column label="对接用户" align="left" width="130">
        <template slot-scope="scope">
          <div>
            <div style="font-weight: bold;">{{ scope.row.userName }}</div>
            <div style="color: #409EFF; font-size: 12px;">{{ scope.row.userPhone }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 获得的联系方式 -->
      <el-table-column label="获得联系方式" align="left" width="130">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.contactName }}</div>
            <div style="color: #409EFF; font-size: 12px;">{{ scope.row.contactPhone }}</div>
            <el-tag size="mini" :type="scope.row.contactSource === '0' ? 'primary' : 'success'">
              {{ scope.row.contactSource === '0' ? '后台' : '发布人' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 对接时间 -->
      <el-table-column label="对接时间" align="center" width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dockingTime, '{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <!-- 联系状态 -->
      <el-table-column label="联系状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isContacted === '1' ? 'success' : 'warning'" size="mini">
            {{ scope.row.isContacted === '1' ? '已联系' : '未联系' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 联系结果 -->
      <el-table-column label="联系结果" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.contactResult">{{ scope.row.contactResult }}</span>
          <span v-else style="color: #C0C4CC;">-</span>
        </template>
      </el-table-column>

      <!-- 对接状态 -->
      <el-table-column label="状态" align="center" width="70">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'info'" size="mini">
            {{ scope.row.status === '0' ? '正常' : '已取消' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)" v-hasPermi="['miniapp:docking:query']">详情</el-button>
          <el-button size="mini" type="text" @click="handleContact(scope.row)" v-hasPermi="['miniapp:docking:edit']">联系</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)" v-hasPermi="['miniapp:docking:remove']" style="color: #F56C6C;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对接详情弹窗 -->
    <el-dialog title="对接详情" :visible.sync="detailDialogVisible" width="70%" append-to-body>
      <div class="detail-content">
        <el-descriptions :column="2" border size="medium">
          <el-descriptions-item label="对接ID" label-style="width: 120px;">
            {{ currentDocking.dockingId }}
          </el-descriptions-item>
          <el-descriptions-item label="需求ID" label-style="width: 120px;">
            {{ currentDocking.demandId }}
          </el-descriptions-item>
          <el-descriptions-item label="需求标题" :span="2" label-style="width: 120px;">
            <span style="font-weight: bold; color: #303133;">
              {{ currentDocking.demandTitle || '未知需求' }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item label="用户姓名" label-style="width: 120px;">
            {{ currentDocking.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="用户电话" label-style="width: 120px;">
            {{ currentDocking.userPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="用户公司" label-style="width: 120px;">
            {{ currentDocking.userCompany || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户职位" label-style="width: 120px;">
            {{ currentDocking.userPosition || '未填写' }}
          </el-descriptions-item>

          <el-descriptions-item label="获得联系人" label-style="width: 120px;">
            {{ currentDocking.contactName }}
          </el-descriptions-item>
          <el-descriptions-item label="获得电话" label-style="width: 120px;">
            {{ currentDocking.contactPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式来源" :span="2" label-style="width: 120px;">
            <el-tag :type="currentDocking.contactSource === '0' ? 'primary' : 'success'" size="small">
              {{ currentDocking.contactSource === '0' ? '后台配置' : '需求发布人' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="对接时间" label-style="width: 120px;">
            {{ parseTime(currentDocking.dockingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="对接状态" label-style="width: 120px;">
            <el-tag :type="currentDocking.status === '0' ? 'success' : 'info'" size="small">
              {{ currentDocking.status === '0' ? '正常' : '已取消' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="创建时间" label-style="width: 120px;">
            {{ parseTime(currentDocking.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" label-style="width: 120px;">
            {{ parseTime(currentDocking.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>

          <el-descriptions-item label="是否已联系" label-style="width: 120px;">
            <el-tag :type="currentDocking.isContacted === '1' ? 'success' : 'warning'" size="small">
              {{ currentDocking.isContacted === '1' ? '已联系' : '未联系' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系结果" label-style="width: 120px;">
            {{ currentDocking.contactResult || '无' }}
          </el-descriptions-item>

          <el-descriptions-item label="联系时间" label-style="width: 120px;">
            {{ currentDocking.contactTime ? parseTime(currentDocking.contactTime, '{y}-{m}-{d} {h}:{i}:{s}') : '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系备注" label-style="width: 120px;">
            {{ currentDocking.contactNotes || '无' }}
          </el-descriptions-item>

          <el-descriptions-item label="备注" :span="2" label-style="width: 120px;">
            {{ currentDocking.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 联系记录弹窗 -->
    <el-dialog title="联系记录" :visible.sync="contactDialogVisible" width="50%" append-to-body>
      <el-form ref="contactForm" :model="contactForm" :rules="contactRules" label-width="100px">
        <el-form-item label="是否已联系" prop="isContacted">
          <el-radio-group v-model="contactForm.isContacted">
            <el-radio label="0">未联系</el-radio>
            <el-radio label="1">已联系</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="联系结果" prop="contactResult" v-if="contactForm.isContacted === '1'">
          <el-select v-model="contactForm.contactResult" placeholder="请选择联系结果" clearable>
            <el-option label="联系成功" value="联系成功" />
            <el-option label="无人接听" value="无人接听" />
            <el-option label="号码错误" value="号码错误" />
            <el-option label="拒绝沟通" value="拒绝沟通" />
            <el-option label="稍后联系" value="稍后联系" />
            <el-option label="已有合作" value="已有合作" />
            <el-option label="不感兴趣" value="不感兴趣" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="联系备注" prop="contactNotes">
          <el-input
            v-model="contactForm.contactNotes"
            type="textarea"
            :rows="4"
            placeholder="请输入联系备注，如沟通内容、后续计划等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="联系时间" prop="contactTime" v-if="contactForm.isContacted === '1'">
          <el-date-picker
            v-model="contactForm.contactTime"
            type="datetime"
            placeholder="选择联系时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="contactDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitContactForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDocking, delDocking } from "@/api/miniapp/docking";

export default {
  name: "Docking",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求对接表格数据
      dockingList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        demandTitle: null,
        userName: null,
        status: null,
        dockingTime: null
      },
      // 详情弹窗
      detailDialogVisible: false,
      // 当前查看的对接记录
      currentDocking: {},
      // 联系记录弹窗
      contactDialogVisible: false,
      // 联系记录表单
      contactForm: {
        dockingId: null,
        isContacted: '0',
        contactResult: '',
        contactNotes: '',
        contactTime: ''
      },
      // 联系记录表单验证
      contactRules: {
        isContacted: [
          { required: true, message: "请选择是否已联系", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询需求对接列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.params["beginDockingTime"] = this.dateRange[0];
        this.queryParams.params["endDockingTime"] = this.dateRange[1];
      }
      listDocking(this.queryParams).then(response => {
        this.dockingList = response.rows;
        this.total = response.total;
        this.loading = false;
        // 调试：打印第一条数据查看demandTitle字段
        if (response.rows && response.rows.length > 0) {
          console.log('第一条对接记录数据:', response.rows[0]);
          console.log('需求标题字段:', response.rows[0].demandTitle);
        }
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dockingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    
    /** 查看详情 */
    handleDetail(row) {
      this.currentDocking = row;
      this.detailDialogVisible = true;
    },

    /** 联系记录 */
    handleContact(row) {
      this.contactForm = {
        dockingId: row.dockingId,
        isContacted: row.isContacted || '0',
        contactResult: row.contactResult || '',
        contactNotes: row.contactNotes || '',
        contactTime: row.contactTime || ''
      };
      this.contactDialogVisible = true;
    },

    /** 提交联系记录表单 */
    submitContactForm() {
      this.$refs["contactForm"].validate(valid => {
        if (valid) {
          // 如果选择已联系但没有设置联系时间，使用当前时间
          if (this.contactForm.isContacted === '1' && !this.contactForm.contactTime) {
            this.contactForm.contactTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
          }

          this.$http.put('/miniapp/docking/updateContactStatus', this.contactForm).then(response => {
            if (response.data.code === 200) {
              this.$modal.msgSuccess("联系记录更新成功");
              this.contactDialogVisible = false;
              this.getList();
            } else {
              this.$modal.msgError(response.data.msg || "更新失败");
            }
          }).catch(() => {
            this.$modal.msgError("网络错误");
          });
        }
      });
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const dockingIds = row.dockingId || this.ids;
      this.$modal.confirm('是否确认删除对接记录编号为"' + dockingIds + '"的数据项？').then(function() {
        return delDocking(dockingIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/docking/export', {
        ...this.queryParams
      }, `docking_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.detail-content {
  padding: 10px 0;
}

.el-table .cell {
  word-break: break-word;
}

.el-descriptions-item__label {
  font-weight: bold;
  background-color: #fafafa;
}

.el-tag {
  margin: 2px 0;
}

/* 表格行高调整 */
.el-table td {
  padding: 12px 0;
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-bottom: 10px;
}

/* 操作按钮样式 */
.el-table .el-table__body .el-table__row .el-table__cell:last-child .cell {
  white-space: nowrap;
}

.el-table .el-button--mini {
  padding: 4px 6px;
  margin: 0 1px;
  font-size: 12px;
}

/* 表格整体样式优化 */
.el-table {
  font-size: 13px;
}

.el-table .cell {
  padding: 0 8px;
  line-height: 1.4;
}

/* 标签样式优化 */
.el-tag--mini {
  font-size: 11px;
  padding: 1px 4px;
}
</style>
