package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniHaitangProjectRegistration;

/**
 * 天大海棠杯项目报名记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IMiniHaitangProjectRegistrationService 
{
    /**
     * 查询天大海棠杯项目报名记录
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    public MiniHaitangProjectRegistration selectMiniHaitangProjectRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询天大海棠杯项目报名记录列表
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录集合
     */
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationList(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 查询天大海棠杯项目报名记录列表（关联表单配置名称）
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录集合
     */
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationListWithConfig(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 新增天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int insertMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 修改天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int updateMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 批量删除天大海棠杯项目报名记录
     * 
     * @param registrationIds 需要删除的天大海棠杯项目报名记录主键集合
     * @return 结果
     */
    public int deleteMiniHaitangProjectRegistrationByRegistrationIds(Long[] registrationIds);

    /**
     * 删除天大海棠杯项目报名记录信息
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 结果
     */
    public int deleteMiniHaitangProjectRegistrationByRegistrationId(Long registrationId);


}
