-- 修改mini_notice表结构，添加title字段并修改content字段以支持富文本
-- 执行时间：2025-01-20

-- 1. 添加title字段
ALTER TABLE `mini_notice` 
ADD COLUMN `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '通知标题' AFTER `notice_id`;

-- 2. 修改content字段长度以支持富文本内容
ALTER TABLE `mini_notice` 
MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '通知内容（富文本）';

-- 3. 添加title字段的索引
ALTER TABLE `mini_notice` 
ADD INDEX `idx_title`(`title`) USING BTREE;

-- 4. 更新现有数据，为已有记录设置默认标题
UPDATE `mini_notice` SET `title` = CONCAT('通知-', `notice_id`) WHERE `title` = '' OR `title` IS NULL;
