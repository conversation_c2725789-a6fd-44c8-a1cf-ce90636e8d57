package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.SensitiveWordCategory;

/**
 * 敏感词分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface SensitiveWordCategoryMapper 
{
    /**
     * 查询敏感词分类
     * 
     * @param categoryId 敏感词分类主键
     * @return 敏感词分类
     */
    public SensitiveWordCategory selectSensitiveWordCategoryByCategoryId(Long categoryId);

    /**
     * 查询敏感词分类列表
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 敏感词分类集合
     */
    public List<SensitiveWordCategory> selectSensitiveWordCategoryList(SensitiveWordCategory sensitiveWordCategory);

    /**
     * 查询所有启用的敏感词分类
     * 
     * @return 敏感词分类集合
     */
    public List<SensitiveWordCategory> selectEnabledSensitiveWordCategoryList();

    /**
     * 根据分类编码查询敏感词分类
     * 
     * @param categoryCode 分类编码
     * @return 敏感词分类
     */
    public SensitiveWordCategory selectSensitiveWordCategoryByCategoryCode(String categoryCode);

    /**
     * 新增敏感词分类
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 结果
     */
    public int insertSensitiveWordCategory(SensitiveWordCategory sensitiveWordCategory);

    /**
     * 修改敏感词分类
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 结果
     */
    public int updateSensitiveWordCategory(SensitiveWordCategory sensitiveWordCategory);

    /**
     * 删除敏感词分类
     * 
     * @param categoryId 敏感词分类主键
     * @return 结果
     */
    public int deleteSensitiveWordCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除敏感词分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSensitiveWordCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 检查分类编码是否唯一
     * 
     * @param categoryCode 分类编码
     * @param categoryId 分类ID（排除自己）
     * @return 结果
     */
    public int checkCategoryCodeUnique(String categoryCode, Long categoryId);
}
