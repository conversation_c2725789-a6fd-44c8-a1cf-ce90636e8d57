package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniHaitangProjectFormConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * 天大海棠杯项目报名表单配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Mapper
public interface MiniHaitangProjectFormConfigMapper 
{
    /**
     * 查询天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 天大海棠杯项目报名表单配置
     */
    public MiniHaitangProjectFormConfig selectMiniHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 查询天大海棠杯项目报名表单配置列表
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 天大海棠杯项目报名表单配置集合
     */
    public List<MiniHaitangProjectFormConfig> selectMiniHaitangProjectFormConfigList(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 新增天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int insertMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 修改天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int updateMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 删除天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    public int deleteMiniHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 批量删除天大海棠杯项目报名表单配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniHaitangProjectFormConfigByConfigIds(Long[] configIds);

    /**
     * 查询启用的表单配置
     * 
     * @return 启用的表单配置
     */
    public MiniHaitangProjectFormConfig selectEnabledFormConfig();

    /**
     * 禁用所有表单配置
     * 
     * @return 结果
     */
    public int disableAllFormConfigs();

    /**
     * 启用指定表单配置
     * 
     * @param configId 配置ID
     * @return 结果
     */
    public int enableFormConfig(Long configId);

    /**
     * 查询启用状态的表单配置列表
     * 
     * @return 启用状态的表单配置集合
     */
    public List<MiniHaitangProjectFormConfig> selectEnabledMiniHaitangProjectFormConfigList();
}
