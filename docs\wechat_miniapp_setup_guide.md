# 微信小程序配置指南

## 🎯 配置目标

完成微信小程序登录功能的配置，使后端能够通过code换取openid。

## 📋 配置步骤

### 1. 获取微信小程序配置信息

#### 步骤1: 登录微信公众平台
- 访问: https://mp.weixin.qq.com/
- 使用小程序管理员账号登录

#### 步骤2: 获取AppID
- 进入小程序管理后台
- 点击左侧菜单 "开发" -> "开发管理" -> "开发设置"
- 复制 "AppID(小程序ID)"

#### 步骤3: 获取AppSecret
- 在同一页面找到 "AppSecret(小程序密钥)"
- 点击 "重置" 按钮（如果之前没有生成过）
- 复制生成的AppSecret（注意：只显示一次，请妥善保存）

### 2. 配置服务器域名

#### 在微信公众平台配置
- 开发 -> 开发管理 -> 开发设置 -> 服务器域名
- 在 "request合法域名" 中添加你的后端API域名
- 例如: `https://your-api-domain.com`

### 3. 修改后端配置

#### 编辑 application.yml
找到文件: `ruoyi-admin/src/main/resources/application.yml`

将以下配置中的占位符替换为实际值：
```yaml
# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID - 替换为实际的AppID
    appid: your_miniapp_appid
    # 小程序AppSecret - 替换为实际的AppSecret  
    secret: your_miniapp_secret
    # 微信API基础URL（无需修改）
    api-base-url: https://api.weixin.qq.com
    # 获取access_token的URL（无需修改）
    token-url: /cgi-bin/token
    # code换取openid的URL（无需修改）
    jscode2session-url: /sns/jscode2session
```

#### 配置示例
```yaml
# 微信小程序配置
wechat:
  miniapp:
    appid: wx1234567890abcdef
    secret: abcdef1234567890abcdef1234567890abcdef12
    api-base-url: https://api.weixin.qq.com
    token-url: /cgi-bin/token
    jscode2session-url: /sns/jscode2session
```

### 4. 重启应用

修改配置后需要重启Spring Boot应用使配置生效。

## 🧪 测试配置

### 1. 检查配置是否生效

启动应用后，可以通过日志查看配置是否正确加载：
- 查看启动日志中是否有微信配置相关信息
- 确认没有配置错误的警告信息

### 2. 测试登录接口

#### 使用Postman或其他API测试工具

**请求URL**: `POST http://localhost:8080/miniapp/user/app/weixinLogin`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "code": "test_code_from_miniapp"
}
```

**预期响应**:
- 如果配置正确但code无效，会返回微信接口的错误信息
- 如果配置错误，会返回"微信小程序配置不完整"的错误

### 3. 小程序端测试

在小程序中使用以下代码测试：
```javascript
wx.login({
  success: function(res) {
    if (res.code) {
      console.log('获取到code:', res.code);
      
      wx.request({
        url: 'http://your-server:8080/miniapp/user/app/weixinLogin',
        method: 'POST',
        data: {
          code: res.code
        },
        success: function(response) {
          console.log('登录响应:', response.data);
        },
        fail: function(error) {
          console.error('登录失败:', error);
        }
      });
    }
  }
});
```

## ⚠️ 注意事项

### 安全注意事项
1. **AppSecret保密**: 
   - AppSecret是敏感信息，不能暴露给前端
   - 不要将AppSecret提交到公开的代码仓库
   - 建议使用环境变量或配置文件管理

2. **生产环境配置**:
   - 生产环境建议使用不同的配置文件
   - 可以使用Spring Profile来区分不同环境的配置

### 开发注意事项
1. **code有效期**: 
   - 微信登录凭证code有效期为5分钟
   - 每个code只能使用一次
   - 测试时需要重新获取code

2. **网络环境**:
   - 确保服务器能够访问微信API (api.weixin.qq.com)
   - 如果在内网环境，可能需要配置代理

3. **调试技巧**:
   - 开启DEBUG日志级别查看详细的API调用信息
   - 使用微信开发者工具的网络面板查看请求详情

## 🔧 故障排除

### 常见错误及解决方案

#### 1. "微信小程序配置不完整"
**原因**: AppID或AppSecret配置错误
**解决**: 
- 检查application.yml中的配置
- 确认不是默认的占位符值
- 重启应用

#### 2. "invalid code"
**原因**: code已过期或已使用
**解决**: 
- 重新调用wx.login()获取新的code
- 确认code在5分钟内使用

#### 3. "invalid appid"
**原因**: AppID配置错误
**解决**: 
- 检查AppID是否正确
- 确认AppID对应的是小程序而不是公众号

#### 4. "invalid appsecret"
**原因**: AppSecret配置错误
**解决**: 
- 重新生成AppSecret
- 确认复制的AppSecret完整无误

#### 5. 网络连接失败
**原因**: 服务器无法访问微信API
**解决**: 
- 检查网络连接
- 配置防火墙规则
- 如需要，配置HTTP代理

## 📞 技术支持

如果遇到配置问题，可以：
1. 查看应用日志获取详细错误信息
2. 参考微信官方文档: https://developers.weixin.qq.com/miniprogram/dev/
3. 检查微信公众平台的开发者工具和错误日志

## ✅ 配置完成检查清单

- [ ] 已获取微信小程序AppID
- [ ] 已获取微信小程序AppSecret  
- [ ] 已在微信公众平台配置服务器域名
- [ ] 已修改application.yml配置文件
- [ ] 已重启Spring Boot应用
- [ ] 已测试登录接口响应正常
- [ ] 已在小程序端测试登录流程

完成以上所有步骤后，微信小程序登录功能就配置完成了！
