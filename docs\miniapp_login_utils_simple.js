/**
 * 小程序登录工具类 - 简化版
 * 使用方法：
 * 1. 将此文件放到 utils 目录下
 * 2. 在页面中引入：const LoginUtils = require('../../utils/login-utils.js')
 * 3. 调用相应方法进行登录
 */

const API_BASE_URL = 'https://your-api-domain.com'; // 请替换为实际的API地址

class LoginUtils {
  
  /**
   * 检查登录状态
   */
  static isLoggedIn() {
    const token = wx.getStorageSync('token');
    return !!token;
  }

  /**
   * 获取用户信息
   */
  static getUserInfo() {
    return wx.getStorageSync('userInfo') || null;
  }

  /**
   * 快速登录（仅使用code，不需要手机号）
   */
  static quickLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            this.performLogin(res.code)
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error('获取微信登录凭证失败'));
          }
        },
        fail: (error) => {
          reject(new Error('微信登录失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 手机号授权登录
   */
  static phoneLogin(phoneAuthResult) {
    return new Promise((resolve, reject) => {
      if (phoneAuthResult.errMsg !== 'getPhoneNumber:ok') {
        reject(new Error('用户拒绝手机号授权'));
        return;
      }

      wx.login({
        success: (res) => {
          if (res.code) {
            this.performLoginWithPhone(res.code, phoneAuthResult)
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error('获取微信登录凭证失败'));
          }
        },
        fail: (error) => {
          reject(new Error('微信登录失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 智能登录（推荐使用）
   * 自动检查用户状态并选择合适的登录方式
   */
  static smartLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 先检查用户状态
            this.checkUserStatus(res.code)
              .then((statusData) => {
                const { isRegistered, needPhoneAuth } = statusData;
                
                if (isRegistered && !needPhoneAuth) {
                  // 老用户且信息完整，直接登录
                  return this.performLogin(res.code);
                } else {
                  // 新用户或需要手机号，返回状态让调用方决定
                  resolve({
                    needUserChoice: true,
                    statusData,
                    code: res.code
                  });
                  return;
                }
              })
              .then((loginResult) => {
                if (loginResult && !loginResult.needUserChoice) {
                  resolve(loginResult);
                }
              })
              .catch(() => {
                // 检查状态失败，直接尝试快速登录
                this.performLogin(res.code)
                  .then(resolve)
                  .catch(reject);
              });
          } else {
            reject(new Error('获取微信登录凭证失败'));
          }
        },
        fail: (error) => {
          reject(new Error('微信登录失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 检查用户状态
   */
  static checkUserStatus(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${API_BASE_URL}/miniapp/user/checkUserStatus`,
        method: 'POST',
        data: { code },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '检查用户状态失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 执行普通登录
   */
  static performLogin(code, additionalData = {}) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${API_BASE_URL}/miniapp/user/weixinLogin`,
        method: 'POST',
        data: {
          code,
          ...additionalData
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.saveLoginData(res.data.data);
            resolve({
              success: true,
              userInfo: res.data.data,
              isNewUser: res.data.data.isNewUser
            });
          } else {
            reject(new Error(res.data.msg || '登录失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 执行手机号登录
   */
  static performLoginWithPhone(code, phoneDetail) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${API_BASE_URL}/miniapp/user/weixinLogin`,
        method: 'POST',
        data: {
          code,
          encryptedData: phoneDetail.encryptedData,
          iv: phoneDetail.iv
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.saveLoginData(res.data.data);
            resolve({
              success: true,
              userInfo: res.data.data,
              isNewUser: res.data.data.isNewUser,
              hasPhone: true
            });
          } else {
            reject(new Error(res.data.msg || '登录失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + error.errMsg));
        }
      });
    });
  }

  /**
   * 保存登录数据
   */
  static saveLoginData(userData) {
    wx.setStorageSync('token', userData.token);
    wx.setStorageSync('userInfo', userData);
  }

  /**
   * 退出登录
   */
  static logout() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 可以添加其他清理逻辑
    console.log('用户已退出登录');
  }

  /**
   * 获取请求头（包含token）
   */
  static getAuthHeaders() {
    const token = wx.getStorageSync('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }

  /**
   * 带认证的请求
   */
  static authRequest(options) {
    return new Promise((resolve, reject) => {
      const headers = {
        ...options.header,
        ...this.getAuthHeaders()
      };

      wx.request({
        ...options,
        header: headers,
        success: (res) => {
          if (res.data.code === 401) {
            // token过期，清除登录状态
            this.logout();
            reject(new Error('登录已过期，请重新登录'));
          } else {
            resolve(res);
          }
        },
        fail: reject
      });
    });
  }
}

module.exports = LoginUtils;
