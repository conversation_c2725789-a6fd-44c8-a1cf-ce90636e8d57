{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754299061748}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_formConfig", "require", "_request", "_interopRequireDefault", "name", "dicts", "data", "loading", "listLoading", "ids", "single", "multiple", "total", "formConfigList", "enabledConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "title", "open", "formConfigOpen", "previewDialogVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentConfig", "queryParams", "pageNum", "pageSize", "config<PERSON><PERSON>", "status", "form", "rules", "required", "message", "trigger", "created", "loadEnabledConfig", "getList", "methods", "_this", "request", "url", "method", "then", "response", "loadEna<PERSON><PERSON><PERSON><PERSON><PERSON>s", "catch", "formConfig", "JSON", "parse", "e", "_this2", "listFormConfig", "rows", "cancel", "reset", "configId", "configDescription", "isEnabled", "sortOrder", "createBy", "createTime", "updateBy", "updateTime", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this3", "getFormConfig", "submitForm", "_this4", "$refs", "validate", "valid", "updateFormConfig", "$modal", "msgSuccess", "addFormConfig", "handleDelete", "_this5", "configIds", "confirm", "delFormConfig", "handleFormConfig", "handlePreview", "handleEnable", "_this6", "enableFormConfig", "addFormField", "defaultName", "generateUniqueFieldName", "push", "label", "type", "options", "removeFormField", "index", "splice", "prefix", "counter", "some", "field", "needOptions", "includes", "handleTemplateCommand", "command", "_this7", "templates", "basic", "project", "_objectSpread2", "default", "toLowerCase", "handlePreviewForm", "saveFormConfig", "_this8", "msgError", "i", "concat", "formData", "stringify", "getFieldOptions", "split", "opt", "trim", "filter", "getOptionsPlaceholder", "placeholders", "radio", "checkbox", "select", "radio_other", "checkbox_other", "select_other", "rate", "getFieldIcon", "icons", "input", "textarea", "number", "email", "tel", "date", "time", "datetime", "file", "idcard", "money", "slider", "switch", "color", "getFieldTypeName", "names", "handleExport", "download", "Date", "getTime"], "sources": ["src/views/miniapp/haitang/projectFormConfig/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 表单配置管理区域 -->\n    <div class=\"form-config-container\">\n      <div class=\"config-header\">\n        <h3>项目报名表单配置管理</h3>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd\"\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\n          >新增配置</el-button>\n        </div>\n      </div>\n\n      <div class=\"config-content\" v-loading=\"loading\">\n        <!-- 当前启用的配置 -->\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\n          <div class=\"enabled-header\">\n            <h4>\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\n              当前启用配置：{{ enabledConfig.configName }}\n            </h4>\n            <div class=\"enabled-actions\">\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                icon=\"el-icon-setting\"\n                @click=\"handleFormConfig(enabledConfig)\"\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              >配置表单</el-button>\n              <el-button\n                size=\"small\"\n                type=\"success\"\n                icon=\"el-icon-view\"\n                @click=\"handlePreview(enabledConfig)\"\n              >预览表单</el-button>\n            </div>\n          </div>\n          <div class=\"enabled-description\">\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\n          </div>\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\n            <h5>表单字段预览：</h5>\n            <div class=\"field-list\">\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\n                <div class=\"field-info\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                  <span class=\"field-label\">{{ field.label }}</span>\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div v-else class=\"empty-form\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\n          </div>\n        </div>\n\n        <!-- 无启用配置时的提示 -->\n        <div v-else class=\"no-enabled-config\">\n          <i class=\"el-icon-warning-outline\"></i>\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 所有配置列表 -->\n    <div class=\"config-list-container\">\n      <div class=\"list-header\">\n        <h4>所有表单配置</h4>\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\n          <el-form-item label=\"配置名称\" prop=\"configName\">\n            <el-input\n              v-model=\"queryParams.configName\"\n              placeholder=\"请输入配置名称\"\n              clearable\n              style=\"width: 200px;\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\n              <el-option\n                v-for=\"dict in dict.type.sys_normal_disable\"\n                :key=\"dict.value\"\n                :label=\"dict.label\"\n                :value=\"dict.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\n            <el-tag v-else type=\"info\">未启用</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >配置</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit\"\n              @click=\"handleUpdate(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >编辑</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-check\"\n              @click=\"handleEnable(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              v-if=\"scope.row.isEnabled !== '1'\"\n            >启用</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-delete\"\n              @click=\"handleDelete(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </div>\n\n    <!-- 添加或修改配置基本信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"配置名称\" prop=\"configName\">\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\n        </el-form-item>\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单配置对话框 -->\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\n      <div class=\"form-fields-config\">\n        <!-- 工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\n              <el-button size=\"small\">\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"advanced\">高级字段模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\n          </div>\n        </div>\n\n        <!-- 字段配置列表 -->\n        <div class=\"form-fields-list\">\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\n          </div>\n          <div v-else>\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\n              <div class=\"field-config-header\">\n                <span class=\"field-index\">{{ index + 1 }}</span>\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\">\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\n                  <el-option label=\"📞 电话\" value=\"tel\" />\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                  <el-option label=\"📅 日期\" value=\"date\" />\n                  <el-option label=\"⏰ 时间\" value=\"time\" />\n                  <el-option label=\"📅⏰ 日期时间\" value=\"datetime\" />\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\n                  <el-option label=\"🔗 网址链接\" value=\"url\" />\n                  <el-option label=\"🏢 身份证号\" value=\"idcard\" />\n                  <el-option label=\"💰 金额\" value=\"money\" />\n                  <el-option label=\"📏 评分\" value=\"rate\" />\n                  <el-option label=\"🎚️ 滑块\" value=\"slider\" />\n                  <el-option label=\"🔄 开关\" value=\"switch\" />\n                  <el-option label=\"🌈 颜色选择\" value=\"color\" />\n                </el-select>\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\n              </div>\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\n                <el-input\n                  v-model=\"field.options\"\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\n                  size=\"small\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <!-- 基础输入类型 -->\n              <el-input\n                v-if=\"['input', 'email', 'tel', 'url', 'idcard'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 数字类型 -->\n              <el-input-number\n                v-else-if=\"['number', 'money'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 单选类型 -->\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-radio>\n              </el-radio-group>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\n                  <el-radio\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-radio>\n                  <el-radio label=\"其他\">其他</el-radio>\n                </el-radio-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 多选类型 -->\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-checkbox>\n              </el-checkbox-group>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\n                  <el-checkbox\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-checkbox>\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\n                </el-checkbox-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 下拉选择 -->\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\n                <el-option\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                  :value=\"option\"\n                />\n              </el-select>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\n                  <el-option\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                    :value=\"option\"\n                  />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 日期时间类型 -->\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-time-picker\n                v-else-if=\"field.type === 'time'\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-date-picker\n                v-else-if=\"field.type === 'datetime'\"\n                type=\"datetime\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 评分 -->\n              <el-rate\n                v-else-if=\"field.type === 'rate'\"\n                disabled\n                :max=\"getFieldOptions(field.options).length || 5\"\n              />\n              <!-- 滑块 -->\n              <el-slider\n                v-else-if=\"field.type === 'slider'\"\n                disabled\n                :max=\"100\"\n                style=\"margin: 12px 0;\"\n              />\n              <!-- 开关 -->\n              <el-switch\n                v-else-if=\"field.type === 'switch'\"\n                disabled\n              />\n              <!-- 颜色选择 -->\n              <el-color-picker\n                v-else-if=\"field.type === 'color'\"\n                disabled\n              />\n              <!-- 文件上传 -->\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :auto-upload=\"false\"\n                disabled\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\n              </el-upload>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig } from \"@/api/miniapp/haitang/formConfig\";\nimport request from '@/utils/request';\n\nexport default {\n  name: \"FormConfig\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      listLoading: false,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 天大海棠杯项目报名表单配置表格数据\n      formConfigList: [],\n      // 当前启用的配置\n      enabledConfig: null,\n      // 启用配置的表单字段\n      enabledFormFields: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 当前配置的表单字段\n      currentFormFields: [],\n      // 当前操作的配置\n      currentConfig: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        configName: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        configName: [\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadEnabledConfig();\n    this.getList();\n  },\n  methods: {\n    /** 加载启用的配置 */\n    loadEnabledConfig() {\n      this.loading = true;\n      request({\n        url: '/miniapp/haitang/formConfig/enabled',\n        method: 'get'\n      }).then(response => {\n        if (response.data) {\n          this.enabledConfig = response.data;\n          this.loadEnabledFormFields();\n        } else {\n          this.enabledConfig = null;\n          this.enabledFormFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.enabledConfig = null;\n        this.enabledFormFields = [];\n        this.loading = false;\n      });\n    },\n    /** 加载启用配置的表单字段 */\n    loadEnabledFormFields() {\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\n        try {\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\n        } catch (e) {\n          this.enabledFormFields = [];\n        }\n      } else {\n        this.enabledFormFields = [];\n      }\n    },\n    /** 查询天大海棠杯项目报名表单配置列表 */\n    getList() {\n      this.listLoading = true;\n      listFormConfig(this.queryParams).then(response => {\n        this.formConfigList = response.rows;\n        this.total = response.total;\n        this.listLoading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        configId: null,\n        configName: null,\n        configDescription: null,\n        formConfig: null,\n        isEnabled: \"0\",\n        sortOrder: 0,\n        status: \"0\",\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.configId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加天大海棠杯项目报名表单配置\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const configId = row.configId || this.ids\n      getFormConfig(configId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改天大海棠杯项目报名表单配置\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.configId != null) {\n            updateFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const configIds = row.configId || this.ids;\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\n        return delFormConfig(configIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.formConfigOpen = true;\n    },\n    /** 预览按钮操作 */\n    handlePreview(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 启用按钮操作 */\n    handleEnable(row) {\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\n        return enableFormConfig(row.configId);\n      }).then(() => {\n        this.loadEnabledConfig();\n        this.getList();\n        this.$modal.msgSuccess(\"启用成功\");\n      }).catch(() => {});\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      const defaultName = this.generateUniqueFieldName('field');\n      this.currentFormFields.push({\n        name: defaultName,\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.currentFormFields.splice(index, 1);\n    },\n    /** 生成唯一字段名 */\n    generateUniqueFieldName(prefix) {\n      let counter = 1;\n      let name = prefix + counter;\n      while (this.currentFormFields.some(field => field.name === name)) {\n        counter++;\n        name = prefix + counter;\n      }\n      return name;\n    },\n    /** 判断字段类型是否需要选项 */\n    needOptions(type) {\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'rate'].includes(type);\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$modal.confirm('确认清空所有字段？').then(() => {\n          this.currentFormFields = [];\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' },\n          { label: '身份证号', name: '', type: 'idcard', required: false, options: '' }\n        ],\n        project: [\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '项目阶段', name: '', type: 'select', required: true, options: '创意阶段,初创阶段,成长阶段,成熟阶段' },\n          { label: '预期投资金额', name: '', type: 'money', required: false, options: '' },\n          { label: '项目网站', name: '', type: 'url', required: false, options: '' },\n          { label: '项目评分', name: '', type: 'rate', required: false, options: '1,2,3,4,5' },\n          { label: '是否同意条款', name: '', type: 'switch', required: true, options: '' },\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.currentFormFields = templates[command].map(field => ({\n          ...field,\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\n        }));\n      }\n    },\n    /** 预览表单 */\n    handlePreviewForm() {\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (!this.currentConfig) {\n        this.$modal.msgError(\"请先选择要配置的表单\");\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.currentFormFields.length; i++) {\n        const field = this.currentFormFields[i];\n        if (!field.label) {\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (!field.name) {\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\n        }\n        if (this.needOptions(field.type) && !field.options) {\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\n          return;\n        }\n      }\n\n      const formData = {\n        configId: this.currentConfig.configId,\n        formConfig: JSON.stringify(this.currentFormFields)\n      };\n\n      updateFormConfig(formData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadEnabledConfig();\n        this.getList();\n      });\n    },\n    /** 获取字段选项 */\n    getFieldOptions(options) {\n      if (!options) return [];\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\n    },\n    /** 获取选项输入框占位符 */\n    getOptionsPlaceholder(type) {\n      const placeholders = {\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        rate: '评分等级，用逗号分隔，如：1,2,3,4,5 或 差,一般,良好,优秀,卓越'\n      };\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-circle-check',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus-outline',\n        checkbox_other: 'el-icon-circle-plus-outline',\n        select_other: 'el-icon-circle-plus-outline',\n        date: 'el-icon-date',\n        time: 'el-icon-time',\n        datetime: 'el-icon-date',\n        file: 'el-icon-upload',\n        url: 'el-icon-link',\n        idcard: 'el-icon-postcard',\n        money: 'el-icon-coin',\n        rate: 'el-icon-star-on',\n        slider: 'el-icon-sort',\n        switch: 'el-icon-switch-button',\n        color: 'el-icon-brush'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const names = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        time: '时间',\n        datetime: '日期时间',\n        file: '文件上传',\n        url: '网址链接',\n        idcard: '身份证号',\n        money: '金额',\n        rate: '评分',\n        slider: '滑块',\n        switch: '开关',\n        color: '颜色选择'\n      };\n      return names[type] || '未知类型';\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/haitang/formConfig/export', {\n        ...this.queryParams\n      }, `formConfig_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA2bA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,WAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,oBAAA;MACA;MACAC,iBAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAH,UAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,cACAF,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,gBAAA;QACAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAnC,IAAA;UACA8B,KAAA,CAAAtB,aAAA,GAAA2B,QAAA,CAAAnC,IAAA;UACA8B,KAAA,CAAAM,qBAAA;QACA;UACAN,KAAA,CAAAtB,aAAA;UACAsB,KAAA,CAAArB,iBAAA;QACA;QACAqB,KAAA,CAAA7B,OAAA;MACA,GAAAoC,KAAA;QACAP,KAAA,CAAAtB,aAAA;QACAsB,KAAA,CAAArB,iBAAA;QACAqB,KAAA,CAAA7B,OAAA;MACA;IACA;IACA,kBACAmC,qBAAA,WAAAA,sBAAA;MACA,SAAA5B,aAAA,SAAAA,aAAA,CAAA8B,UAAA;QACA;UACA,KAAA7B,iBAAA,GAAA8B,IAAA,CAAAC,KAAA,MAAAhC,aAAA,CAAA8B,UAAA;QACA,SAAAG,CAAA;UACA,KAAAhC,iBAAA;QACA;MACA;QACA,KAAAA,iBAAA;MACA;IACA;IACA,wBACAmB,OAAA,WAAAA,QAAA;MAAA,IAAAc,MAAA;MACA,KAAAxC,WAAA;MACA,IAAAyC,0BAAA,OAAA3B,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAnC,cAAA,GAAA4B,QAAA,CAAAS,IAAA;QACAF,MAAA,CAAApC,KAAA,GAAA6B,QAAA,CAAA7B,KAAA;QACAoC,MAAA,CAAAxC,WAAA;MACA;IACA;IACA;IACA2C,MAAA,WAAAA,OAAA;MACA,KAAAlC,IAAA;MACA,KAAAmC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzB,IAAA;QACA0B,QAAA;QACA5B,UAAA;QACA6B,iBAAA;QACAV,UAAA;QACAW,SAAA;QACAC,SAAA;QACA9B,MAAA;QACA+B,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzC,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACA8B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzD,GAAA,GAAAyD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAf,QAAA;MAAA;MACA,KAAA3C,MAAA,GAAAwD,SAAA,CAAAG,MAAA;MACA,KAAA1D,QAAA,IAAAuD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAlB,KAAA;MACA,KAAAnC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA;MACA,IAAAC,QAAA,GAAAmB,GAAA,CAAAnB,QAAA,SAAA5C,GAAA;MACA,IAAAiE,yBAAA,EAAArB,QAAA,EAAAb,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAA9C,IAAA,GAAAc,QAAA,CAAAnC,IAAA;QACAmE,MAAA,CAAAxD,IAAA;QACAwD,MAAA,CAAAzD,KAAA;MACA;IACA;IACA,WACA2D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAjD,IAAA,CAAA0B,QAAA;YACA,IAAA2B,4BAAA,EAAAJ,MAAA,CAAAjD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAA1C,OAAA;YACA;UACA;YACA,IAAAiD,yBAAA,EAAAP,MAAA,CAAAjD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,SAAA,GAAAd,GAAA,CAAAnB,QAAA,SAAA5C,GAAA;MACA,KAAAwE,MAAA,CAAAM,OAAA,6BAAAD,SAAA,aAAA9C,IAAA;QACA,WAAAgD,yBAAA,EAAAF,SAAA;MACA,GAAA9C,IAAA;QACA6C,MAAA,CAAAnD,OAAA;QACAmD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAvC,KAAA;IACA;IACA,eACA8C,gBAAA,WAAAA,iBAAAjB,GAAA;MACA,KAAAnD,aAAA,GAAAmD,GAAA;MACA,KAAApD,iBAAA;MACA,IAAAoD,GAAA,CAAA5B,UAAA;QACA;UACA,KAAAxB,iBAAA,GAAAyB,IAAA,CAAAC,KAAA,CAAA0B,GAAA,CAAA5B,UAAA;QACA,SAAAG,CAAA;UACA,KAAA3B,iBAAA;QACA;MACA;MACA,KAAAF,cAAA;IACA;IACA,aACAwE,aAAA,WAAAA,cAAAlB,GAAA;MACA,KAAAnD,aAAA,GAAAmD,GAAA;MACA,KAAApD,iBAAA;MACA,IAAAoD,GAAA,CAAA5B,UAAA;QACA;UACA,KAAAxB,iBAAA,GAAAyB,IAAA,CAAAC,KAAA,CAAA0B,GAAA,CAAA5B,UAAA;QACA,SAAAG,CAAA;UACA,KAAA3B,iBAAA;QACA;MACA;MACA,KAAAD,oBAAA;IACA;IACA,aACAwE,YAAA,WAAAA,aAAAnB,GAAA;MAAA,IAAAoB,MAAA;MACA,KAAAX,MAAA,CAAAM,OAAA,2BAAA/C,IAAA;QACA,WAAAqD,4BAAA,EAAArB,GAAA,CAAAnB,QAAA;MACA,GAAAb,IAAA;QACAoD,MAAA,CAAA3D,iBAAA;QACA2D,MAAA,CAAA1D,OAAA;QACA0D,MAAA,CAAAX,MAAA,CAAAC,UAAA;MACA,GAAAvC,KAAA;IACA;IACA,aACAmD,YAAA,WAAAA,aAAA;MACA,IAAAC,WAAA,QAAAC,uBAAA;MACA,KAAA5E,iBAAA,CAAA6E,IAAA;QACA7F,IAAA,EAAA2F,WAAA;QACAG,KAAA;QACAC,IAAA;QACAtE,QAAA;QACAuE,OAAA;MACA;IACA;IACA,aACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAlF,iBAAA,CAAAmF,MAAA,CAAAD,KAAA;IACA;IACA,cACAN,uBAAA,WAAAA,wBAAAQ,MAAA;MACA,IAAAC,OAAA;MACA,IAAArG,IAAA,GAAAoG,MAAA,GAAAC,OAAA;MACA,YAAArF,iBAAA,CAAAsF,IAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAvG,IAAA,KAAAA,IAAA;MAAA;QACAqG,OAAA;QACArG,IAAA,GAAAoG,MAAA,GAAAC,OAAA;MACA;MACA,OAAArG,IAAA;IACA;IACA,mBACAwG,WAAA,WAAAA,YAAAT,IAAA;MACA,gGAAAU,QAAA,CAAAV,IAAA;IACA;IACA,aACAW,qBAAA,WAAAA,sBAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAA9B,MAAA,CAAAM,OAAA,cAAA/C,IAAA;UACAwE,MAAA,CAAA5F,iBAAA;QACA;QACA;MACA;MAEA,IAAA6F,SAAA;QACAC,KAAA,GACA;UAAAhB,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,EACA;QACAe,OAAA,GACA;UAAAjB,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA,GACA;UAAAF,KAAA;UAAA9F,IAAA;UAAA+F,IAAA;UAAAtE,QAAA;UAAAuE,OAAA;QAAA;MAEA;MAEA,IAAAa,SAAA,CAAAF,OAAA;QACA,KAAA3F,iBAAA,GAAA6F,SAAA,CAAAF,OAAA,EAAA5C,GAAA,WAAAwC,KAAA;UAAA,WAAAS,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAV,KAAA;YACAvG,IAAA,EAAA4G,MAAA,CAAAhB,uBAAA,CAAAW,KAAA,CAAAT,KAAA,CAAAoB,WAAA;UAAA;QAAA,CACA;MACA;IACA;IACA,WACAC,iBAAA,WAAAA,kBAAA;MACA,KAAApG,oBAAA;IACA;IACA,aACAqG,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAApG,aAAA;QACA,KAAA4D,MAAA,CAAAyC,QAAA;QACA;MACA;;MAEA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAAvG,iBAAA,CAAAiD,MAAA,EAAAsD,CAAA;QACA,IAAAhB,KAAA,QAAAvF,iBAAA,CAAAuG,CAAA;QACA,KAAAhB,KAAA,CAAAT,KAAA;UACA,KAAAjB,MAAA,CAAAyC,QAAA,UAAAE,MAAA,CAAAD,CAAA;UACA;QACA;QACA,KAAAhB,KAAA,CAAAvG,IAAA;UACAuG,KAAA,CAAAvG,IAAA,QAAA4F,uBAAA,CAAAW,KAAA,CAAAT,KAAA,CAAAoB,WAAA;QACA;QACA,SAAAV,WAAA,CAAAD,KAAA,CAAAR,IAAA,MAAAQ,KAAA,CAAAP,OAAA;UACA,KAAAnB,MAAA,CAAAyC,QAAA,UAAAE,MAAA,CAAAD,CAAA,8BAAAC,MAAA,CAAAjB,KAAA,CAAAT,KAAA;UACA;QACA;MACA;MAEA,IAAA2B,QAAA;QACAxE,QAAA,OAAAhC,aAAA,CAAAgC,QAAA;QACAT,UAAA,EAAAC,IAAA,CAAAiF,SAAA,MAAA1G,iBAAA;MACA;MAEA,IAAA4D,4BAAA,EAAA6C,QAAA,EAAArF,IAAA,WAAAC,QAAA;QACAgF,MAAA,CAAAxC,MAAA,CAAAC,UAAA;QACAuC,MAAA,CAAAvG,cAAA;QACAuG,MAAA,CAAAxF,iBAAA;QACAwF,MAAA,CAAAvF,OAAA;MACA;IACA;IACA,aACA6F,eAAA,WAAAA,gBAAA3B,OAAA;MACA,KAAAA,OAAA;MACA,OAAAA,OAAA,CAAA4B,KAAA,MAAA7D,GAAA,WAAA8D,GAAA;QAAA,OAAAA,GAAA,CAAAC,IAAA;MAAA,GAAAC,MAAA,WAAAF,GAAA;QAAA,OAAAA,GAAA;MAAA;IACA;IACA,iBACAG,qBAAA,WAAAA,sBAAAjC,IAAA;MACA,IAAAkC,YAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,IAAA;MACA;MACA,OAAAP,YAAA,CAAAlC,IAAA;IACA;IACA,aACA0C,YAAA,WAAAA,aAAA1C,IAAA;MACA,IAAA2C,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAb,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAS,IAAA;QACAC,IAAA;QACAC,QAAA;QACAC,IAAA;QACAjH,GAAA;QACAkH,MAAA;QACAC,KAAA;QACAb,IAAA;QACAc,MAAA;QACAC,MAAA;QACAC,KAAA;MACA;MACA,OAAAd,KAAA,CAAA3C,IAAA;IACA;IACA,eACA0D,gBAAA,WAAAA,iBAAA1D,IAAA;MACA,IAAA2D,KAAA;QACAf,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAb,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAS,IAAA;QACAC,IAAA;QACAC,QAAA;QACAC,IAAA;QACAjH,GAAA;QACAkH,MAAA;QACAC,KAAA;QACAb,IAAA;QACAc,MAAA;QACAC,MAAA;QACAC,KAAA;MACA;MACA,OAAAE,KAAA,CAAA3D,IAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,0CAAA5C,cAAA,CAAAC,OAAA,MACA,KAAA/F,WAAA,iBAAAsG,MAAA,CACA,IAAAqC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}