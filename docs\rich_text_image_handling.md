# 富文本编辑器图片上传和回显机制详解

## 概述
本文档详细说明了项目中富文本编辑器（Quill）的图片上传和回显机制，包括路径拼接、存储方式和前端展示。

## 图片上传流程

### 1. 上传配置
**前端配置** (`ruoyi-ui/src/components/Editor/index.vue`)
```javascript
data() {
  return {
    uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传接口
    headers: {
      Authorization: "Bearer " + getToken()
    }
  }
}
```

**环境变量配置**
- 开发环境 (`.env.development`): `VUE_APP_BASE_API = '/dev-api'`
- 生产环境 (`.env.production`): `VUE_APP_BASE_API = '/prod-api'`

### 2. 后端上传处理
**上传接口** (`ruoyi-admin/src/main/java/com/ruoyi/web/controller/common/CommonController.java`)
```java
@PostMapping("/upload")
public AjaxResult uploadFile(@RequestPart("file") MultipartFile file) throws Exception {
    // 上传文件路径
    String filePath = RuoYiConfig.getUploadPath();
    // 上传并返回新文件名称
    String fileName = FileUploadUtils.upload(filePath, file);
    String url = serverConfig.getUrl() + fileName;
    
    AjaxResult ajax = AjaxResult.success();
    ajax.put("url", url);           // 完整URL
    ajax.put("fileName", fileName); // 相对路径
    ajax.put("newFileName", FileUtils.getName(fileName));
    ajax.put("originalFilename", file.getOriginalFilename());
    return ajax;
}
```

### 3. 文件存储路径
**配置文件** (`ruoyi-admin/src/main/resources/application.yml`)
```yaml
ruoyi:
  # 文件路径 示例（Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
```

**文件命名规则** (`FileUploadUtils.extractFilename()`)
```
格式: {日期路径}/{原文件名}_{序列号}.{扩展名}
示例: 2025/01/20/image_123456.jpg
```

## 图片回显机制

### 1. 上传成功后的处理
**前端处理** (`Editor/index.vue` 的 `handleUploadSuccess` 方法)
```javascript
handleUploadSuccess(res, file) {
  if (res.code == 200) {
    let quill = this.Quill
    let length = quill.getSelection().index
    // 插入图片，使用完整URL路径
    quill.insertEmbed(length, "image", process.env.VUE_APP_BASE_API + res.fileName)
    quill.setSelection(length + 1)
  }
}
```

### 2. 路径拼接规则
**图片URL构成**
```
完整图片URL = 前端BASE_API + 后端返回的fileName
```

**示例**
- 开发环境: `/dev-api/profile/upload/2025/01/20/image_123456.jpg`
- 生产环境: `/prod-api/profile/upload/2025/01/20/image_123456.jpg`

### 3. 服务器URL获取
**ServerConfig类** (`ruoyi-framework/src/main/java/com/ruoyi/framework/config/ServerConfig.java`)
```java
public String getUrl() {
    HttpServletRequest request = ServletUtils.getRequest();
    return getDomain(request);
}

public static String getDomain(HttpServletRequest request) {
    StringBuffer url = request.getRequestURL();
    String contextPath = request.getServletContext().getContextPath();
    return url.delete(url.length() - request.getRequestURI().length(), url.length())
              .append(contextPath).toString();
}
```

## 静态资源访问配置

### 1. 资源映射配置
**ResourcesConfig** (`ruoyi-framework/src/main/java/com/ruoyi/framework/config/ResourcesConfig.java`)
```java
@Override
public void addResourceHandlers(ResourceHandlerRegistry registry) {
    /** 本地文件上传路径 */
    registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**")
            .addResourceLocations("file:" + RuoYiConfig.getProfile() + "/");
}
```

### 2. 安全配置
**SecurityConfig** 允许匿名访问静态资源
```java
.antMatchers(HttpMethod.GET, "/profile/**").permitAll()
```

## 数据库存储

### 1. 富文本内容存储
富文本内容以HTML格式存储在数据库中，包含完整的图片路径：
```html
<p>这是一段包含图片的富文本内容</p>
<p><img src="/dev-api/profile/upload/2025/01/20/image_123456.jpg"></p>
<p>图片后的文字内容</p>
```

### 2. 字段类型
- **数据库字段**: `content` TEXT 类型
- **支持长度**: 最大65,535字符（足够存储大量富文本内容）

## 前端展示

### 1. 编辑时回显
当从数据库读取富文本内容时，Quill编辑器会自动解析HTML并显示图片：
```javascript
// Quill自动处理HTML内容
this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)
```

### 2. 只读展示
在小程序端或其他展示页面，直接渲染HTML内容即可显示图片。

## 注意事项

### 1. 跨域问题
- 开发环境通过代理解决跨域
- 生产环境需要配置正确的域名和端口

### 2. 文件清理
- 删除富文本内容时，相关图片文件不会自动删除
- 建议定期清理无用的上传文件

### 3. 路径兼容性
- 图片路径使用相对路径存储，便于环境迁移
- 前端通过BASE_API动态拼接完整URL

### 4. 安全考虑
- 上传文件类型限制
- 文件大小限制（默认5MB）
- 文件名安全处理（防止路径遍历攻击）

## 故障排查

### 1. 图片无法显示
- 检查文件是否存在于服务器指定路径
- 检查静态资源映射配置
- 检查前端BASE_API配置

### 2. 上传失败
- 检查上传目录权限
- 检查文件大小限制
- 检查文件类型限制

### 3. 路径错误
- 检查环境变量配置
- 检查ServerConfig的URL生成逻辑
- 检查代理配置（开发环境）
