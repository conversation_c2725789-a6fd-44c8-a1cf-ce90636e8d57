package com.ruoyi.miniapp.controller;

import java.util.List;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniEnterprise;
import com.ruoyi.miniapp.domain.dto.EnterpriseIndustryUpdateDTO;
import com.ruoyi.miniapp.service.IMiniEnterpriseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.miniapp.service.impl.MiniEnterpriseServiceImpl.EnterpriseIndustryVO;

/**
 * 企业管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/miniapp/enterprise")
public class MiniEnterpriseController extends BaseController
{
    @Autowired
    private IMiniEnterpriseService miniEnterpriseService;

    /**
     * 查询企业管理列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniEnterprise miniEnterprise)
    {
        startPage();
        List<MiniEnterprise> list = miniEnterpriseService.selectMiniEnterpriseList(miniEnterprise);
        return getDataTable(list);
    }

    /**
     * 导出企业管理列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:export')")
    @Log(title = "企业管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniEnterprise miniEnterprise)
    {
        List<MiniEnterprise> list = miniEnterpriseService.selectMiniEnterpriseList(miniEnterprise);
        ExcelUtil<MiniEnterprise> util = new ExcelUtil<MiniEnterprise>(MiniEnterprise.class);
        util.exportExcel(response, list, "企业管理数据");
    }

    /**
     * 获取企业管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:query')")
    @GetMapping(value = "/{enterpriseId}")
    public AjaxResult getInfo(@PathVariable("enterpriseId") Long enterpriseId)
    {
        return AjaxResult.success(miniEnterpriseService.selectMiniEnterpriseByEnterpriseId(enterpriseId));
    }

    /**
     * 新增企业管理
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:add')")
    @Log(title = "企业管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniEnterprise miniEnterprise)
    {
        return toAjax(miniEnterpriseService.insertMiniEnterprise(miniEnterprise));
    }

    /**
     * 修改企业管理
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:edit')")
    @Log(title = "企业管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniEnterprise miniEnterprise)
    {
        return toAjax(miniEnterpriseService.updateMiniEnterprise(miniEnterprise));
    }

    /**
     * 删除企业管理
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:remove')")
    @Log(title = "企业管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{enterpriseIds}")
    public AjaxResult remove(@PathVariable Long[] enterpriseIds)
    {
        return toAjax(miniEnterpriseService.deleteMiniEnterpriseByEnterpriseIds(enterpriseIds));
    }

    /**
     * 获取企业产业关联信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:query')")
    @GetMapping("/industry/{enterpriseId}")
    public AjaxResult getEnterpriseIndustry(@PathVariable("enterpriseId") Long enterpriseId)
    {
        // 返回VO列表，带全路径
        return AjaxResult.success(miniEnterpriseService.selectEnterpriseIndustryByEnterpriseId(enterpriseId));
    }

    /**
     * 更新企业产业关联
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:edit')")
    @Log(title = "企业产业关联", businessType = BusinessType.UPDATE)
    @PutMapping("/industry")
    public AjaxResult updateEnterpriseIndustry(@RequestBody MiniEnterprise miniEnterprise)
    {
        return toAjax(miniEnterpriseService.updateEnterpriseIndustry(miniEnterprise));
    }

    /**
     * 更新企业产业关联（简化版本）
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:edit')")
    @Log(title = "企业产业关联", businessType = BusinessType.UPDATE)
    @PostMapping("/industry/update")
    public AjaxResult updateEnterpriseIndustrySimple(@RequestBody EnterpriseIndustryUpdateDTO dto)
    {
        // 构建企业对象
        MiniEnterprise miniEnterprise = new MiniEnterprise();
        miniEnterprise.setEnterpriseId(dto.getEnterpriseId());

        // 构建产业关联列表
        List<MiniEnterprise.MiniEnterpriseIndustry> industryList = new ArrayList<>();
        if (dto.getIndustryTreeIds() != null && !dto.getIndustryTreeIds().isEmpty()) {
            for (Long industryTreeId : dto.getIndustryTreeIds()) {
                MiniEnterprise.MiniEnterpriseIndustry industry = new MiniEnterprise.MiniEnterpriseIndustry();
                industry.setEnterpriseId(dto.getEnterpriseId());
                industry.setIndustryTreeId(industryTreeId);
                industryList.add(industry);
            }
        }
        miniEnterprise.setIndustryList(industryList);

        return toAjax(miniEnterpriseService.updateEnterpriseIndustry(miniEnterprise));
    }

    /**
     * 获取企业详细信息（包含产业信息）
     */
    @PreAuthorize("@ss.hasPermi('miniapp:enterprise:query')")
    @GetMapping("/detail/{enterpriseId}")
    public AjaxResult getEnterpriseDetail(@PathVariable("enterpriseId") Long enterpriseId)
    {
        return AjaxResult.success(miniEnterpriseService.selectEnterpriseDetailByEnterpriseId(enterpriseId));
    }


} 