package com.ruoyi.miniapp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信小程序配置类
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Component
@ConfigurationProperties(prefix = "wechat.miniapp")
public class WechatMiniappConfig
{
    /** 小程序AppID */
    private String appid;

    /** 小程序AppSecret */
    private String secret;

    /** 微信API基础URL */
    private String apiBaseUrl;

    /** 获取access_token的URL */
    private String tokenUrl;

    /** code换取openid的URL */
    private String jscode2sessionUrl;

    public String getAppid()
    {
        return appid;
    }

    public void setAppid(String appid)
    {
        this.appid = appid;
    }

    public String getSecret()
    {
        return secret;
    }

    public void setSecret(String secret)
    {
        this.secret = secret;
    }

    public String getApiBaseUrl()
    {
        return apiBaseUrl;
    }

    public void setApiBaseUrl(String apiBaseUrl)
    {
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getTokenUrl()
    {
        return tokenUrl;
    }

    public void setTokenUrl(String tokenUrl)
    {
        this.tokenUrl = tokenUrl;
    }

    public String getJscode2sessionUrl()
    {
        return jscode2sessionUrl;
    }

    public void setJscode2sessionUrl(String jscode2sessionUrl)
    {
        this.jscode2sessionUrl = jscode2sessionUrl;
    }

    /**
     * 获取完整的jscode2session URL
     */
    public String getFullJscode2sessionUrl()
    {
        return apiBaseUrl + jscode2sessionUrl;
    }

    @Override
    public String toString()
    {
        return "WechatMiniappConfig{" +
                "appid='" + appid + '\'' +
                ", secret='[HIDDEN]'" +
                ", apiBaseUrl='" + apiBaseUrl + '\'' +
                ", tokenUrl='" + tokenUrl + '\'' +
                ", jscode2sessionUrl='" + jscode2sessionUrl + '\'' +
                '}';
    }
}
