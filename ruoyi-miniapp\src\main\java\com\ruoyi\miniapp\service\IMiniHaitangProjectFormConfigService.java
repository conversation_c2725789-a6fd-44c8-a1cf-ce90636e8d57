package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniHaitangProjectFormConfig;

/**
 * 天大海棠杯项目报名表单配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IMiniHaitangProjectFormConfigService 
{
    /**
     * 查询天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 天大海棠杯项目报名表单配置
     */
    public MiniHaitangProjectFormConfig selectMiniHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 查询天大海棠杯项目报名表单配置列表
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 天大海棠杯项目报名表单配置集合
     */
    public List<MiniHaitangProjectFormConfig> selectMiniHaitangProjectFormConfigList(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 新增天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int insertMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 修改天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int updateMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig);

    /**
     * 批量删除天大海棠杯项目报名表单配置
     * 
     * @param configIds 需要删除的天大海棠杯项目报名表单配置主键集合
     * @return 结果
     */
    public int deleteMiniHaitangProjectFormConfigByConfigIds(Long[] configIds);

    /**
     * 删除天大海棠杯项目报名表单配置信息
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    public int deleteMiniHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 查询启用的表单配置
     * 
     * @return 启用的表单配置
     */
    public MiniHaitangProjectFormConfig selectEnabledFormConfig();

    /**
     * 启用表单配置（同时禁用其他配置）
     * 
     * @param configId 配置ID
     * @return 结果
     */
    public int enableFormConfig(Long configId);

    /**
     * 查询启用状态的表单配置列表
     * 
     * @return 启用状态的表单配置集合
     */
    public List<MiniHaitangProjectFormConfig> selectEnabledMiniHaitangProjectFormConfigList();
}
