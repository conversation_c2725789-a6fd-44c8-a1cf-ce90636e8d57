import request from '@/utils/request'

// 查询天大海棠杯项目报名表单配置列表
export function listFormConfig(query) {
  return request({
    url: '/miniapp/haitang/formConfig/list',
    method: 'get',
    params: query
  })
}

// 查询天大海棠杯项目报名表单配置详细
export function getFormConfig(configId) {
  return request({
    url: '/miniapp/haitang/formConfig/' + configId,
    method: 'get'
  })
}

// 新增天大海棠杯项目报名表单配置
export function addFormConfig(data) {
  return request({
    url: '/miniapp/haitang/formConfig',
    method: 'post',
    data: data
  })
}

// 修改天大海棠杯项目报名表单配置
export function updateFormConfig(data) {
  return request({
    url: '/miniapp/haitang/formConfig',
    method: 'put',
    data: data
  })
}

// 删除天大海棠杯项目报名表单配置
export function delFormConfig(configId) {
  return request({
    url: '/miniapp/haitang/formConfig/' + configId,
    method: 'delete'
  })
}

// 启用表单配置
export function enableFormConfig(configId) {
  return request({
    url: '/miniapp/haitang/formConfig/enable/' + configId,
    method: 'put'
  })
}

// 获取启用的表单配置
export function getEnabledFormConfig() {
  return request({
    url: '/miniapp/haitang/formConfig/enabled',
    method: 'get'
  })
}

// 导出天大海棠杯项目报名表单配置
export function exportFormConfig(query) {
  return request({
    url: '/miniapp/haitang/formConfig/export',
    method: 'post',
    params: query
  })
}
