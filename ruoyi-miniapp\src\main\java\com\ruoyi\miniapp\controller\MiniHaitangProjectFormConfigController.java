package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniHaitangProjectFormConfig;
import com.ruoyi.miniapp.service.IMiniHaitangProjectFormConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 天大海棠杯项目报名表单配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "天大海棠杯项目报名表单配置管理")
@RestController
@RequestMapping("/miniapp/haitang/formConfig")
public class MiniHaitangProjectFormConfigController extends BaseController
{
    @Autowired
    private IMiniHaitangProjectFormConfigService miniHaitangProjectFormConfigService;

    /**
     * 查询天大海棠杯项目报名表单配置列表
     */
    @ApiOperation("查询天大海棠杯项目报名表单配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        startPage();
        List<MiniHaitangProjectFormConfig> list = miniHaitangProjectFormConfigService.selectMiniHaitangProjectFormConfigList(miniHaitangProjectFormConfig);
        return getDataTable(list);
    }

    /**
     * 导出天大海棠杯项目报名表单配置列表
     */
    @ApiOperation("导出天大海棠杯项目报名表单配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:export')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        List<MiniHaitangProjectFormConfig> list = miniHaitangProjectFormConfigService.selectMiniHaitangProjectFormConfigList(miniHaitangProjectFormConfig);
        ExcelUtil<MiniHaitangProjectFormConfig> util = new ExcelUtil<MiniHaitangProjectFormConfig>(MiniHaitangProjectFormConfig.class);
        util.exportExcel(response, list, "天大海棠杯项目报名表单配置数据");
    }

    /**
     * 获取天大海棠杯项目报名表单配置详细信息
     */
    @ApiOperation("获取天大海棠杯项目报名表单配置详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@ApiParam("配置ID") @PathVariable("configId") Long configId)
    {
        return AjaxResult.success(miniHaitangProjectFormConfigService.selectMiniHaitangProjectFormConfigByConfigId(configId));
    }

    /**
     * 新增天大海棠杯项目报名表单配置
     */
    @ApiOperation("新增天大海棠杯项目报名表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:add')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        miniHaitangProjectFormConfig.setCreateBy(getUsername());
        return toAjax(miniHaitangProjectFormConfigService.insertMiniHaitangProjectFormConfig(miniHaitangProjectFormConfig));
    }

    /**
     * 修改天大海棠杯项目报名表单配置
     */
    @ApiOperation("修改天大海棠杯项目报名表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:edit')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        miniHaitangProjectFormConfig.setUpdateBy(getUsername());
        return toAjax(miniHaitangProjectFormConfigService.updateMiniHaitangProjectFormConfig(miniHaitangProjectFormConfig));
    }

    /**
     * 删除天大海棠杯项目报名表单配置
     */
    @ApiOperation("删除天大海棠杯项目报名表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:remove')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@ApiParam("配置ID数组") @PathVariable Long[] configIds)
    {
        try {
            return toAjax(miniHaitangProjectFormConfigService.deleteMiniHaitangProjectFormConfigByConfigIds(configIds));
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 启用表单配置
     */
    @ApiOperation("启用表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:edit')")
    @Log(title = "启用表单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{configId}")
    public AjaxResult enableFormConfig(@ApiParam("配置ID") @PathVariable Long configId)
    {
        return toAjax(miniHaitangProjectFormConfigService.enableFormConfig(configId));
    }

    /**
     * 获取启用的表单配置
     */
    @ApiOperation("获取启用的表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:formConfig:query')")
    @GetMapping("/enabled")
    public AjaxResult getEnabledFormConfig()
    {
        MiniHaitangProjectFormConfig config = miniHaitangProjectFormConfigService.selectEnabledFormConfig();
        return AjaxResult.success(config);
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的表单配置（小程序端）
     */
    @ApiOperation("获取启用的表单配置（小程序端）")
    @GetMapping("/app/enabled")
    public AjaxResult getEnabledFormConfigForApp()
    {
        MiniHaitangProjectFormConfig config = miniHaitangProjectFormConfigService.selectEnabledFormConfig();
        if (config == null || !"0".equals(config.getStatus())) {
            return AjaxResult.error("暂无可用的报名表单配置");
        }
        return AjaxResult.success(config);
    }
}
