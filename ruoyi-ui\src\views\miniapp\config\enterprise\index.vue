<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入企业名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业规模" prop="scale">
        <el-select v-model="queryParams.scale" placeholder="请选择企业规模" clearable size="small">
          <el-option label="小型" value="small" />
          <el-option label="中型" value="medium" />
          <el-option label="大型" value="large" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:enterprise:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:enterprise:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:enterprise:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:enterprise:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="enterpriseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" :show-overflow-tooltip="true" />
      <el-table-column label="法人代表" align="center" prop="legalPerson" width="100" />
      <el-table-column label="联系人" align="center" prop="contactPerson" width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
      <el-table-column label="企业规模" align="center" prop="scale" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.scale">
            {{ getScaleLabel(scope.row.scale) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:enterprise:edit']"
          >修改</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleIndustryConfig(scope.row)"
            v-hasPermi="['miniapp:enterprise:edit']"
          >行业配置</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:enterprise:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="企业名称" prop="enterpriseName">
              <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人代表" prop="legalPerson">
              <el-input v-model="form.legalPerson" placeholder="请输入法人代表" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="企业地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入企业地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="官网地址" prop="website">
              <el-input v-model="form.website" placeholder="请输入官网地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="企业规模" prop="scale">
              <el-select v-model="form.scale" placeholder="请选择企业规模">
                <el-option label="小型" value="small" />
                <el-option label="中型" value="medium" />
                <el-option label="大型" value="large" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成立日期" prop="foundingDate">
              <el-date-picker
                v-model="form.foundingDate"
                type="date"
                placeholder="选择成立日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="企业简介" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入企业简介" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 行业配置对话框 -->
    <el-dialog
      title="行业配置"
      :visible.sync="industryConfigOpen"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div v-if="currentEnterprise">
        <p class="mb-3">
          <strong>企业名称：</strong>{{ currentEnterprise.enterpriseName }}
        </p>

        <div class="industry-config-content">
          <div class="config-header">
            <h4>请选择企业所属行业：</h4>
            <div class="expand-controls">
              <el-button
                size="mini"
                type="text"
                @click="expandAll"
                icon="el-icon-plus"
              >
                全部展开
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="collapseAll"
                icon="el-icon-minus"
              >
                全部收缩
              </el-button>
            </div>
          </div>
          <div class="industry-tree-container">
            <div v-for="level1 in industryTreeData" :key="level1.id" class="level1-item">
              <div class="level1-header" @click="toggleLevel1Expand(level1.id)">
                <i
                  :class="isLevel1Expanded(level1.id) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
                  class="expand-icon"
                ></i>
                <el-checkbox
                  :value="isLevel1Selected(level1)"
                  @change="handleLevel1Change(level1, $event)"
                  :indeterminate="isLevel1Indeterminate(level1)"
                  @click.stop
                >
                  {{ level1.nodeName }}
                </el-checkbox>
              </div>

              <transition name="slide-fade">
                <div
                  v-if="level1.children && level1.children.length > 0 && isLevel1Expanded(level1.id)"
                  class="level2-container"
                >
                <div v-for="level2 in level1.children" :key="level2.id" class="level2-item">
                  <div class="level2-header" @click="toggleLevel2Expand(level2.id)">
                    <i
                      :class="hasLevel3Children(level2) ? (isLevel2Expanded(level2.id) ? 'el-icon-arrow-down' : 'el-icon-arrow-right') : 'el-icon-minus'"
                      class="expand-icon"
                      :style="{ visibility: hasLevel3Children(level2) ? 'visible' : 'hidden' }"
                    ></i>
                    <el-checkbox
                      :value="isLevel2Selected(level2)"
                      @change="handleLevel2Change(level2, $event)"
                      :indeterminate="isLevel2Indeterminate(level2)"
                      @click.stop
                    >
                      {{ level2.nodeName }}
                      <el-tag size="mini" type="info" v-if="level2.streamType">
                        {{ getStreamTypeLabel(level2.streamType) }}
                      </el-tag>
                    </el-checkbox>
                  </div>

                  <transition name="slide-fade">
                    <div
                      v-if="level2.children && level2.children.length > 0 && isLevel2Expanded(level2.id)"
                      class="level3-container"
                    >
                      <el-checkbox-group v-model="selectedIndustryIds" class="level3-group">
                        <el-checkbox
                          v-for="level3 in level2.children"
                          :key="level3.id"
                          :label="level3.id"
                          class="level3-checkbox"
                        >
                          {{ level3.nodeName }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </transition>

                  <!-- 如果第二层级没有子节点，则可以直接选择第二层级 -->
                  <div v-if="!hasLevel3Children(level2)" class="level2-direct-select">
                    <span class="direct-select-hint">此项可直接选择</span>
                  </div>
                </div>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveIndustryConfig">保 存</el-button>
        <el-button @click="cancelIndustryConfig">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listEnterprise, getEnterprise, delEnterprise, addEnterprise, updateEnterprise, getEnterpriseIndustry, updateEnterpriseIndustrySimple } from "@/api/miniapp/enterprise";
import { getIndustryTree } from "@/api/miniapp/industry";

export default {
  name: "Enterprise",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业表格数据
      enterpriseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示行业配置对话框
      industryConfigOpen: false,
      // 当前配置行业的企业
      currentEnterprise: null,
      // 行业树数据
      industryTreeData: [],
      // 已选择的行业ID列表
      selectedIndustryIds: [],
      // 第一层级展开状态
      level1ExpandedIds: [],
      // 第二层级展开状态
      level2ExpandedIds: [],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        enterpriseName: null,
        scale: null,
        status: null
      },
      // 表单参数
      form: {},


      // 表单校验
      rules: {
        enterpriseName: [
          { required: true, message: "企业名称不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  computed: {
    // 暂无计算属性
  },
  methods: {
    /** 查询企业列表 */
    getList() {
      this.loading = true;
      listEnterprise(this.queryParams).then(response => {
        this.enterpriseList = response.rows;
        this.total = response.total;
      }).catch(error => {
        console.error('获取企业列表失败:', error);
        const errorMessage = error.response?.data?.msg || error.message || '获取企业列表失败';
        this.$modal.msgError(errorMessage);
        this.enterpriseList = [];
        this.total = 0;
      }).finally(() => {
        this.loading = false;
      });
    },

    /** 取消企业编辑 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 重置企业表单 */
    reset() {
      this.form = {
        enterpriseId: null,
        enterpriseName: null,
        legalPerson: null,
        address: null,
        contactPerson: null,
        contactPhone: null,
        contactEmail: null,
        description: null,
        scale: null,
        foundingDate: null,
        website: null,
        logoUrl: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理表格多选变化 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.enterpriseId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const enterpriseId = row.enterpriseId || this.ids
      getEnterprise(enterpriseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业";
      }).catch(error => {
        console.error('获取企业详情失败:', error);
        const errorMessage = error.response?.data?.msg || error.message || '获取企业详情失败';
        this.$modal.msgError(errorMessage);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.enterpriseId != null) {
            updateEnterprise(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改企业失败:', error);
              const errorMessage = error.response?.data?.msg || error.message || '修改企业失败';
              this.$modal.msgError(errorMessage);
            });
          } else {
            addEnterprise(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增企业失败:', error);
              const errorMessage = error.response?.data?.msg || error.message || '新增企业失败';
              this.$modal.msgError(errorMessage);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const enterpriseIds = row.enterpriseId || this.ids;
      this.$modal.confirm('是否确认删除企业编号为"' + enterpriseIds + '"的数据项？').then(() => {
        return delEnterprise(enterpriseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        if (error !== 'cancel') {
          console.error('删除企业失败:', error);
          const errorMessage = error.response?.data?.msg || error.message || '删除企业失败';
          this.$modal.msgError(errorMessage);
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('/miniapp/enterprise/export', {
          ...this.queryParams
        }, `enterprise_${new Date().getTime()}.xlsx`)
      } catch (error) {
        console.error('导出企业数据失败:', error);
        this.$modal.msgError('导出失败，请重试');
      }
    },

    /** 获取企业规模标签 */
    getScaleLabel(scale) {
      const scaleMap = {
        'small': '小型',
        'medium': '中型',
        'large': '大型'
      };
      return scaleMap[scale] || scale;
    },

    /** 行业配置按钮操作 */
    handleIndustryConfig(row) {
      this.currentEnterprise = row;
      this.industryConfigOpen = true;
      this.level1ExpandedIds = [];
      this.level2ExpandedIds = [];
      this.getIndustryTreeData();
      this.getEnterpriseIndustryConfig(row.enterpriseId);
    },

    /** 获取行业树数据 */
    async getIndustryTreeData() {
      try {
        const response = await getIndustryTree();
        if (response.code === 200) {
          this.industryTreeData = response.data;
          // 默认展开第一层级
          this.level1ExpandedIds = this.industryTreeData.map(item => item.id);
        } else {
          this.$modal.msgError('获取行业树数据失败');
        }
      } catch (error) {
        console.error('获取行业树数据失败:', error);
        this.$modal.msgError('获取行业树数据失败');
      }
    },

    /** 获取企业行业配置 */
    async getEnterpriseIndustryConfig(enterpriseId) {
      try {
        const response = await getEnterpriseIndustry(enterpriseId);
        if (response.code === 200) {
          this.selectedIndustryIds = response.data.map(item => item.industryTreeId);
        } else {
          this.$modal.msgError('获取企业行业配置失败');
        }
      } catch (error) {
        console.error('获取企业行业配置失败:', error);
        this.$modal.msgError('获取企业行业配置失败');
      }
    },

    /** 保存行业配置 */
    async saveIndustryConfig() {
      try {
        const data = {
          enterpriseId: this.currentEnterprise.enterpriseId,
          industryTreeIds: this.selectedIndustryIds
        };
        const response = await updateEnterpriseIndustrySimple(data);
        if (response.code === 200) {
          this.$modal.msgSuccess('行业配置保存成功');
          this.industryConfigOpen = false;
        } else {
          this.$modal.msgError(response.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存行业配置失败:', error);
        this.$modal.msgError('保存行业配置失败');
      }
    },

    /** 取消行业配置 */
    cancelIndustryConfig() {
      this.industryConfigOpen = false;
      this.currentEnterprise = null;
      this.selectedIndustryIds = [];
      this.level1ExpandedIds = [];
      this.level2ExpandedIds = [];
    },

    /** 获取产业链位置标签 */
    getStreamTypeLabel(streamType) {
      const streamTypeMap = {
        'upstream': '上游',
        'midstream': '中游',
        'downstream': '下游'
      };
      return streamTypeMap[streamType] || streamType;
    },

    /** 判断第一层级是否选中 */
    isLevel1Selected(level1) {
      if (!level1.children || level1.children.length === 0) return false;
      const allLevel3Ids = this.getAllLevel3Ids(level1);
      return allLevel3Ids.length > 0 && allLevel3Ids.every(id => this.selectedIndustryIds.includes(id));
    },

    /** 判断第一层级是否半选中 */
    isLevel1Indeterminate(level1) {
      if (!level1.children || level1.children.length === 0) return false;
      const allLevel3Ids = this.getAllLevel3Ids(level1);
      const selectedCount = allLevel3Ids.filter(id => this.selectedIndustryIds.includes(id)).length;
      return selectedCount > 0 && selectedCount < allLevel3Ids.length;
    },

    /** 处理第一层级选择变化 */
    handleLevel1Change(level1, checked) {
      const allLevel3Ids = this.getAllLevel3Ids(level1);
      if (checked) {
        // 选中所有第三层级
        allLevel3Ids.forEach(id => {
          if (!this.selectedIndustryIds.includes(id)) {
            this.selectedIndustryIds.push(id);
          }
        });
      } else {
        // 取消选中所有第三层级
        this.selectedIndustryIds = this.selectedIndustryIds.filter(id => !allLevel3Ids.includes(id));
      }
    },

    /** 判断第二层级是否选中 */
    isLevel2Selected(level2) {
      if (!level2.children || level2.children.length === 0) {
        // 如果没有子节点，检查第二层级本身是否被选中
        return this.selectedIndustryIds.includes(level2.id);
      }
      const allLevel3Ids = level2.children.map(child => child.id);
      return allLevel3Ids.length > 0 && allLevel3Ids.every(id => this.selectedIndustryIds.includes(id));
    },

    /** 判断第二层级是否半选中 */
    isLevel2Indeterminate(level2) {
      if (!level2.children || level2.children.length === 0) {
        // 如果没有子节点，不会有半选中状态
        return false;
      }
      const allLevel3Ids = level2.children.map(child => child.id);
      const selectedCount = allLevel3Ids.filter(id => this.selectedIndustryIds.includes(id)).length;
      return selectedCount > 0 && selectedCount < allLevel3Ids.length;
    },

    /** 处理第二层级选择变化 */
    handleLevel2Change(level2, checked) {
      if (!level2.children || level2.children.length === 0) {
        // 如果没有子节点，直接处理第二层级的选择
        this.handleLevel2DirectChange(level2, checked);
        return;
      }
      const allLevel3Ids = level2.children.map(child => child.id);
      if (checked) {
        // 选中所有第三层级
        allLevel3Ids.forEach(id => {
          if (!this.selectedIndustryIds.includes(id)) {
            this.selectedIndustryIds.push(id);
          }
        });
      } else {
        // 取消选中所有第三层级
        this.selectedIndustryIds = this.selectedIndustryIds.filter(id => !allLevel3Ids.includes(id));
      }
    },

    /** 获取第一层级下所有第三层级ID */
    getAllLevel3Ids(level1) {
      const level3Ids = [];
      if (level1.children) {
        level1.children.forEach(level2 => {
          if (level2.children && level2.children.length > 0) {
            level2.children.forEach(level3 => {
              level3Ids.push(level3.id);
            });
          } else {
            // 如果第二层级没有子节点，则第二层级本身可以被选择
            level3Ids.push(level2.id);
          }
        });
      }
      return level3Ids;
    },

    /** 处理第二层级直接选择变化 */
    handleLevel2DirectChange(level2, checked) {
      if (checked) {
        // 选中第二层级
        if (!this.selectedIndustryIds.includes(level2.id)) {
          this.selectedIndustryIds.push(level2.id);
        }
      } else {
        // 取消选中第二层级
        this.selectedIndustryIds = this.selectedIndustryIds.filter(id => id !== level2.id);
      }
    },

    /** 切换第一层级展开状态 */
    toggleLevel1Expand(level1Id) {
      const index = this.level1ExpandedIds.indexOf(level1Id);
      if (index > -1) {
        this.level1ExpandedIds.splice(index, 1);
      } else {
        this.level1ExpandedIds.push(level1Id);
      }
    },

    /** 判断第一层级是否展开 */
    isLevel1Expanded(level1Id) {
      return this.level1ExpandedIds.includes(level1Id);
    },

    /** 切换第二层级展开状态 */
    toggleLevel2Expand(level2Id) {
      const index = this.level2ExpandedIds.indexOf(level2Id);
      if (index > -1) {
        this.level2ExpandedIds.splice(index, 1);
      } else {
        this.level2ExpandedIds.push(level2Id);
      }
    },

    /** 判断第二层级是否展开 */
    isLevel2Expanded(level2Id) {
      return this.level2ExpandedIds.includes(level2Id);
    },

    /** 判断第二层级是否有第三层级子节点 */
    hasLevel3Children(level2) {
      return level2.children && level2.children.length > 0;
    },

    /** 全部展开 */
    expandAll() {
      // 展开所有第一层级
      this.level1ExpandedIds = this.industryTreeData.map(item => item.id);

      // 展开所有第二层级
      const allLevel2Ids = [];
      this.industryTreeData.forEach(level1 => {
        if (level1.children) {
          level1.children.forEach(level2 => {
            if (this.hasLevel3Children(level2)) {
              allLevel2Ids.push(level2.id);
            }
          });
        }
      });
      this.level2ExpandedIds = allLevel2Ids;
    },

    /** 全部收缩 */
    collapseAll() {
      this.level1ExpandedIds = [];
      this.level2ExpandedIds = [];
    }














  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb-3 {
  margin-bottom: 16px;
}

// 行业配置对话框样式
.industry-config-content {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      color: #303133;
      font-weight: 600;
    }

    .expand-controls {
      .el-button {
        margin-left: 8px;
        font-size: 12px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}

.industry-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.level1-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.level1-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;

  .expand-icon {
    margin-right: 8px;
    font-size: 14px;
    color: #909399;
    transition: transform 0.3s ease;

    &:hover {
      color: #409eff;
    }
  }

  .el-checkbox {
    font-weight: 600;
    font-size: 16px;

    ::v-deep .el-checkbox__label {
      color: #409eff;
    }
  }
}

.level2-container {
  margin-left: 20px;
  padding-left: 16px;
  border-left: 2px solid #e4e7ed;
}

.level2-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.level2-header {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;

  .expand-icon {
    margin-right: 8px;
    font-size: 12px;
    color: #909399;
    transition: transform 0.3s ease;

    &:hover {
      color: #409eff;
    }
  }

  .el-checkbox {
    font-weight: 500;

    ::v-deep .el-checkbox__label {
      color: #606266;
    }
  }

  .el-tag {
    margin-left: 8px;
  }
}

.level3-container {
  margin-left: 20px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
}

.level3-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.level3-checkbox {
  margin-right: 0 !important;
  margin-bottom: 8px;

  ::v-deep .el-checkbox__label {
    color: #909399;
    font-size: 14px;
  }

  &.is-checked {
    ::v-deep .el-checkbox__label {
      color: #409eff;
      font-weight: 500;
    }
  }
}

.level2-direct-select {
  margin-left: 28px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
  margin-top: 8px;

  .direct-select-hint {
    color: #909399;
    font-size: 12px;
    font-style: italic;
  }
}

.level2-direct-checkbox {
  ::v-deep .el-checkbox__label {
    color: #909399;
    font-size: 14px;
    font-style: italic;
  }

  &.is-checked {
    ::v-deep .el-checkbox__label {
      color: #409eff;
      font-weight: 500;
    }
  }
}

// 展开收缩动画
.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}


</style>