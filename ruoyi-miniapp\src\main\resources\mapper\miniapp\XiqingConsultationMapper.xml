<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.XiqingConsultationMapper">
    
    <resultMap type="XiqingConsultation" id="XiqingConsultationResult">
        <result property="consultationId"    column="consultation_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactMethod"    column="contact_method"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectXiqingConsultationVo">
        select consultation_id, contact_name, contact_method, status, create_by, create_time, update_by, update_time, remark from xiqing_consultation
    </sql>

    <select id="selectXiqingConsultationList" parameterType="XiqingConsultation" resultMap="XiqingConsultationResult">
        <include refid="selectXiqingConsultationVo"/>
        <where>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactMethod != null  and contactMethod != ''"> and contact_method like concat('%', #{contactMethod}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectXiqingConsultationByConsultationId" parameterType="Long" resultMap="XiqingConsultationResult">
        <include refid="selectXiqingConsultationVo"/>
        where consultation_id = #{consultationId}
    </select>
        
    <insert id="insertXiqingConsultation" parameterType="XiqingConsultation" useGeneratedKeys="true" keyProperty="consultationId">
        insert into xiqing_consultation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactMethod != null and contactMethod != ''">contact_method,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactMethod != null and contactMethod != ''">#{contactMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateXiqingConsultation" parameterType="XiqingConsultation">
        update xiqing_consultation
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactMethod != null and contactMethod != ''">contact_method = #{contactMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where consultation_id = #{consultationId}
    </update>

    <delete id="deleteXiqingConsultationByConsultationId" parameterType="Long">
        delete from xiqing_consultation where consultation_id = #{consultationId}
    </delete>

    <delete id="deleteXiqingConsultationByConsultationIds" parameterType="String">
        delete from xiqing_consultation where consultation_id in 
        <foreach item="consultationId" collection="array" open="(" separator="," close=")">
            #{consultationId}
        </foreach>
    </delete>
</mapper>
