# 小程序管理后台 REST API 文档

## 📋 接口说明

所有接口均采用 **POST** 请求方式，使用 JSON 格式传输数据。

### 🔐 权限说明

- **管理端接口**：需要管理员权限，使用 `@PreAuthorize` 注解控制
- **小程序端接口**：路径包含 `/app/`，供小程序调用
- **响应格式**：使用若依框架的 `AjaxResult` 统一响应格式

---

## 🏠 1. 用户管理模块

### 1.1 小程序用户管理 (MiniUserController)

**基础路径**: `/miniapp/user`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询用户列表 | `/list` | `miniapp:user:list` | 分页查询用户列表 |
| 导出用户列表 | `/export` | `miniapp:user:export` | 导出Excel |
| 获取用户详情 | `/getInfo` | `miniapp:user:query` | 根据ID获取用户信息 |
| 新增用户 | `/add` | `miniapp:user:add` | 新增用户 |
| 修改用户 | `/edit` | `miniapp:user:edit` | 修改用户信息 |
| 删除用户 | `/remove` | `miniapp:user:remove` | 批量删除用户 |
| 根据微信OpenID查询 | `/getByWxOpenId` | `miniapp:user:query` | 通过微信OpenID查询用户 |
| 根据手机号查询 | `/getByPhone` | `miniapp:user:query` | 通过手机号查询用户 |
| 完善基本信息 | `/completeBasicInfo` | `miniapp:user:edit` | 完善用户基本信息(30积分) |
| 完善教育背景 | `/completeEducationInfo` | `miniapp:user:edit` | 完善教育背景(20积分) |
| 完善职业信息 | `/completeCareerInfo` | `miniapp:user:edit` | 完善职业信息(25积分) |
| 完善个人展示 | `/completeProfileInfo` | `miniapp:user:edit` | 完善个人展示(15积分) |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 微信登录注册 | `/app/weixinLogin` | 微信授权登录/注册 |
| 手机号登录注册 | `/app/phoneLogin` | 手机号登录/注册 |
| 获取用户信息 | `/app/getUserInfo` | 获取用户详细信息 |

**微信登录参数示例**:
```json
{
  "wxOpenId": "openid_xxx",
  "wxNickname": "用户昵称", 
  "wxAvatarUrl": "头像URL"
}
```

### 1.2 用户积分记录管理 (MiniUserPointsController)

**基础路径**: `/miniapp/userPoints`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询积分记录列表 | `/list` | `miniapp:userPoints:list` | 分页查询积分记录 |
| 导出积分记录 | `/export` | `miniapp:userPoints:export` | 导出Excel |
| 获取积分记录详情 | `/getInfo` | `miniapp:userPoints:query` | 根据ID获取积分记录 |
| 新增积分记录 | `/add` | `miniapp:userPoints:add` | 新增积分记录 |
| 修改积分记录 | `/edit` | `miniapp:userPoints:edit` | 修改积分记录 |
| 删除积分记录 | `/remove` | `miniapp:userPoints:remove` | 批量删除积分记录 |
| 查询用户积分记录 | `/getUserPointsList` | `miniapp:userPoints:list` | 获取指定用户的积分记录 |
| 手动添加积分 | `/addPointsRecord` | `miniapp:userPoints:add` | 手动为用户添加积分 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取用户积分记录 | `/app/getUserPoints` | 获取用户的积分明细 |

**手动添加积分参数示例**:
```json
{
  "userId": 1,
  "pointsChange": 10,
  "changeType": "admin_add",
  "changeReason": "管理员奖励"
}
```

---

## 🎯 2. 内容管理模块

### 2.1 轮播图管理 (MiniBannerController)

**基础路径**: `/miniapp/banner`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询轮播图列表 | `/list` | `miniapp:banner:list` | 分页查询轮播图 |
| 导出轮播图 | `/export` | `miniapp:banner:export` | 导出Excel |
| 获取轮播图详情 | `/getInfo` | `miniapp:banner:query` | 根据ID获取轮播图 |
| 新增轮播图 | `/add` | `miniapp:banner:add` | 新增轮播图 |
| 修改轮播图 | `/edit` | `miniapp:banner:edit` | 修改轮播图 |
| 删除轮播图 | `/remove` | `miniapp:banner:remove` | 批量删除轮播图 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取启用的轮播图 | `/app/getEnabledList` | 获取状态为启用的轮播图列表 |

### 2.2 滚动通知管理 (MiniNoticeController)

**基础路径**: `/miniapp/notice`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询通知列表 | `/list` | `miniapp:notice:list` | 分页查询通知 |
| 导出通知 | `/export` | `miniapp:notice:export` | 导出Excel |
| 获取通知详情 | `/getInfo` | `miniapp:notice:query` | 根据ID获取通知 |
| 新增通知 | `/add` | `miniapp:notice:add` | 新增通知 |
| 修改通知 | `/edit` | `miniapp:notice:edit` | 修改通知 |
| 删除通知 | `/remove` | `miniapp:notice:remove` | 批量删除通知 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取启用的通知 | `/app/getEnabledList` | 获取状态为启用的通知列表 |

### 2.3 弹幕管理 (MiniBarrageController)

**基础路径**: `/miniapp/barrage`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询弹幕列表 | `/list` | `miniapp:barrage:list` | 分页查询弹幕 |
| 导出弹幕 | `/export` | `miniapp:barrage:export` | 导出Excel |
| 获取弹幕详情 | `/getInfo` | `miniapp:barrage:query` | 根据ID获取弹幕 |
| 新增弹幕 | `/add` | `miniapp:barrage:add` | 新增弹幕 |
| 修改弹幕 | `/edit` | `miniapp:barrage:edit` | 修改弹幕 |
| 删除弹幕 | `/remove` | `miniapp:barrage:remove` | 批量删除弹幕 |
| **审核弹幕** | `/audit` | `miniapp:barrage:audit` | 审核弹幕(通过/拒绝) |
| **批量审核通过** | `/batchApprove` | `miniapp:barrage:audit` | 批量审核通过 |
| **批量审核拒绝** | `/batchReject` | `miniapp:barrage:audit` | 批量审核拒绝 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取通过审核的弹幕 | `/app/getApprovedList` | 获取审核通过的弹幕列表 |
| 发布弹幕 | `/app/publish` | 用户发布弹幕(待审核状态) |

**审核弹幕参数示例**:
```json
{
  "barrageId": 1,
  "auditStatus": "1",  // 1-通过 2-拒绝
  "auditRemark": "审核通过"
}
```

**批量拒绝参数示例**:
```json
{
  "barrageIds": [1, 2, 3],
  "rejectReason": "内容不符合规范"
}
```

---

## 🏷️ 3. 标签管理模块

### 3.1 标签管理 (MiniTagController)

**基础路径**: `/miniapp/tag`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询标签列表 | `/list` | `miniapp:tag:list` | 分页查询标签 |
| 导出标签 | `/export` | `miniapp:tag:export` | 导出Excel |
| 获取标签详情 | `/getInfo` | `miniapp:tag:query` | 根据ID获取标签 |
| 新增标签 | `/add` | `miniapp:tag:add` | 新增标签 |
| 修改标签 | `/edit` | `miniapp:tag:edit` | 修改标签 |
| 删除标签 | `/remove` | `miniapp:tag:remove` | 批量删除标签 |
| 根据分类查询标签 | `/getTagsByCategory` | `miniapp:tag:list` | 根据分类ID查询标签 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取启用的标签 | `/app/getEnabledList` | 获取所有启用的标签 |
| 根据分类获取标签 | `/app/getTagsByCategory` | 根据分类获取标签列表 |

---

## 🎪 4. 业务功能模块

### 4.1 活动报名管理 (MiniEventController)

**基础路径**: `/miniapp/event`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询活动列表 | `/list` | `miniapp:event:list` | 分页查询活动 |
| 导出活动 | `/export` | `miniapp:event:export` | 导出Excel |
| 获取活动详情 | `/getInfo` | `miniapp:event:query` | 根据ID获取活动 |
| 新增活动 | `/add` | `miniapp:event:add` | 新增活动 |
| 修改活动 | `/edit` | `miniapp:event:edit` | 修改活动 |
| 删除活动 | `/remove` | `miniapp:event:remove` | 批量删除活动 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取正在进行的活动 | `/app/getActiveList` | 获取当前可报名的活动 |
| 获取启用的活动 | `/app/getEnabledList` | 获取所有启用的活动 |
| 获取活动详情 | `/app/getDetail` | 获取活动详细信息 |

### 4.2 用户报名记录管理 (MiniEventRegistrationController)

**基础路径**: `/miniapp/eventRegistration`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询报名记录列表 | `/list` | `miniapp:eventRegistration:list` | 分页查询报名记录 |
| 导出报名记录 | `/export` | `miniapp:eventRegistration:export` | 导出Excel |
| 获取报名记录详情 | `/getInfo` | `miniapp:eventRegistration:query` | 根据ID获取报名记录 |
| 新增报名记录 | `/add` | `miniapp:eventRegistration:add` | 新增报名记录 |
| 修改报名记录 | `/edit` | `miniapp:eventRegistration:edit` | 修改报名记录 |
| 删除报名记录 | `/remove` | `miniapp:eventRegistration:remove` | 批量删除报名记录 |
| 查询用户报名记录 | `/getUserRegistrations` | `miniapp:eventRegistration:list` | 获取指定用户的报名记录 |
| 查询活动报名记录 | `/getEventRegistrations` | `miniapp:eventRegistration:list` | 获取指定活动的报名记录 |
| 检查用户是否已报名 | `/checkUserRegistered` | `miniapp:eventRegistration:query` | 检查用户是否已报名某活动 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 用户报名活动 | `/app/register` | 用户报名参加活动 |
| 获取用户报名记录 | `/app/getUserRegistrations` | 获取用户的报名记录 |
| 检查报名状态 | `/app/checkRegistrationStatus` | 检查用户是否已报名 |

**报名参数示例**:
```json
{
  "userId": 1,
  "eventId": 1,
  "registrationData": "{\"name\":\"张三\",\"phone\":\"13800138000\",\"email\":\"<EMAIL>\"}"
}
```

### 4.3 需求信息管理 (MiniDemandController)

**基础路径**: `/miniapp/demand`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询需求列表 | `/list` | `miniapp:demand:list` | 分页查询需求 |
| 导出需求 | `/export` | `miniapp:demand:export` | 导出Excel |
| 获取需求详情 | `/getInfo` | `miniapp:demand:query` | 根据ID获取需求 |
| 新增需求 | `/add` | `miniapp:demand:add` | 新增需求 |
| 修改需求 | `/edit` | `miniapp:demand:edit` | 修改需求 |
| 删除需求 | `/remove` | `miniapp:demand:remove` | 批量删除需求 |
| 根据分类查询需求 | `/getDemandsByCategory` | `miniapp:demand:list` | 根据分类ID查询需求 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取推荐需求 | `/app/getRecommendedList` | 获取推荐的需求信息 |
| 获取启用的需求 | `/app/getEnabledList` | 获取所有启用的需求 |
| 根据分类获取需求 | `/app/getDemandsByCategory` | 根据分类获取需求列表 |
| 获取需求详情 | `/app/getDetail` | 获取需求详细信息 |

---

## 🌟 5. 小程序综合API

### 5.1 综合API (MiniAppApiController)

**基础路径**: `/miniapp/api`

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| **获取首页数据** | `/getHomeData` | 一次性获取首页所需的所有数据 |
| 获取需求对接平台数据 | `/getDemandData` | 获取需求平台相关数据 |
| 获取活动报名数据 | `/getEventData` | 获取活动相关数据 |
| 获取标签数据 | `/getTagData` | 获取标签相关数据 |
| 获取页面内容 | `/getPageContent` | 根据页面标识获取内容 |
| 获取关于我们 | `/getAboutUs` | 获取关于我们页面内容 |
| 获取用户协议 | `/getUserAgreement` | 获取用户协议内容 |
| 获取隐私协议 | `/getPrivacyPolicy` | 获取隐私协议内容 |
| 获取所有页面内容 | `/getAllPageContents` | 获取所有页面内容 |
| 综合搜索 | `/search` | 跨模块搜索功能 |

**首页数据返回示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "banners": [...],      // 轮播图数据
    "notices": [...],      // 滚动通知数据
    "barrages": [...],     // 弹幕数据
    "activities": [...],   // 推荐活动
    "techStars": [...],    // 科技之星
    "jobs": [...]          // 推荐招聘
  }
}
```

---

## 🔄 6. 统一响应格式

### 6.1 成功响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体的数据内容
  }
}
```

### 6.2 失败响应

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 6.3 分页响应

```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 100,
  "rows": [
    // 数据列表
  ]
}
```

---

## 🔐 7. 权限标识说明

### 7.1 权限命名规范

权限标识采用 `模块:功能:操作` 的格式：

- `miniapp:user:list` - 小程序用户查询权限
- `miniapp:user:add` - 小程序用户新增权限
- `miniapp:user:edit` - 小程序用户修改权限
- `miniapp:user:remove` - 小程序用户删除权限
- `miniapp:user:export` - 小程序用户导出权限
- `miniapp:barrage:audit` - 弹幕审核权限

### 7.2 特殊权限

- **弹幕审核**: `miniapp:barrage:audit` - 弹幕审核专用权限
- **积分管理**: `miniapp:userPoints:add` - 手动添加积分权限

---

## 📝 8. 接口调用示例

### 8.1 管理端调用示例

```javascript
// 查询用户列表
fetch('/miniapp/user/list', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    nickname: '张三',
    status: '1'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### 8.2 小程序端调用示例

```javascript
// 获取首页数据
wx.request({
  url: 'https://api.example.com/miniapp/api/getHomeData',
  method: 'POST',
  data: {},
  success: function(res) {
    console.log(res.data);
  }
});

// 用户微信登录
wx.request({
  url: 'https://api.example.com/miniapp/user/app/weixinLogin',
  method: 'POST',
  data: {
    wxOpenId: 'openid_xxx',
    wxNickname: '用户昵称',
    wxAvatarUrl: 'https://example.com/avatar.jpg'
  },
  success: function(res) {
    if (res.data.code === 200) {
      // 登录成功
      const userInfo = res.data.data;
      console.log(userInfo);
    }
  }
});
```

---

## 🏆 9. 创赛路演模块

### 9.1 项目报名管理 (ProjectRegistrationController)

**基础路径**: `/miniapp/haitang/project`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询项目报名列表 | `/list` | `miniapp:haitang:project:list` | 分页查询项目报名 |
| 导出项目报名 | `/export` | `miniapp:haitang:project:export` | 导出Excel |
| 获取项目报名详情 | `/{id}` | `miniapp:haitang:project:query` | 根据ID获取项目报名 |
| 删除项目报名 | `/{ids}` | `miniapp:haitang:project:remove` | 批量删除项目报名 |
| 审核项目报名 | `/audit` | `miniapp:haitang:project:audit` | 审核项目报名 |
| 获取赞助商图片 | `/config/sponsor` | `miniapp:haitang:project:query` | 获取当前赞助商图片 |
| 更新赞助商图片 | `/config/sponsor` | `miniapp:haitang:project:edit` | 更新赞助商图片 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 提交项目报名 | `/app/register` | 小程序端提交项目报名资料 |
| 获取赞助商图片 | `/app/config/sponsor` | 小程序端获取当前赞助商图片 |

**项目报名参数示例**:
```json
{
  "projectName": "智能AI助手项目",
  "teamSize": 5,
  "city": "天津",
  "competitionArea": "华北赛区",
  "industry": "人工智能",
  "isTjuAlumni": true,
  "projectDescription": "基于大语言模型的智能助手系统...",
  "hasCompany": true,
  "companyName": "天津智能科技有限公司",
  "lastYearRevenue": 1000000.00,
  "projectValuation": 5000000.00,
  "latestFundingRound": "A轮",
  "investmentInstitution": "红杉资本",
  "companyLogo": "https://example.com/logo.png",
  "projectBp": "https://example.com/bp.pdf",
  "recommender": "张教授",
  "sponsorUnit": "https://example.com/sponsor-logo.png",
  "contactName": "李明",
  "contactPhone": "13800138000",
  "contactWechat": "liming123",
  "contactPosition": "项目负责人"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

**赞助商图片更新参数示例**:
```json
{
  "sponsorUnit": "https://example.com/new-sponsor-logo.png"
}
```

**获取赞助商图片响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "https://example.com/sponsor-logo.png"
}
```

### 9.2 项目指导活动管理 (ProjectGuidanceController)

**基础路径**: `/miniapp/haitang/guidance`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询项目指导活动列表 | `/list` | `miniapp:haitang:guidance:list` | 分页查询项目指导活动 |
| 导出项目指导活动 | `/export` | `miniapp:haitang:guidance:export` | 导出Excel |
| 获取项目指导活动详情 | `/{guidanceId}` | `miniapp:haitang:guidance:query` | 根据ID获取项目指导活动 |
| 新增项目指导活动 | `/add` | `miniapp:haitang:guidance:add` | 新增项目指导活动 |
| 修改项目指导活动 | `/edit` | `miniapp:haitang:guidance:edit` | 修改项目指导活动 |
| 删除项目指导活动 | `/{guidanceIds}` | `miniapp:haitang:guidance:remove` | 批量删除项目指导活动 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 提交指导活动报名 | `/app/register` | 小程序端提交指导活动报名资料 |

**指导活动报名参数示例**:
```json
{
  "guidanceId": 1,
  "userId": 123,
  "formData": "{\"name\":\"张三\",\"phone\":\"13800138000\",\"email\":\"<EMAIL>\",\"company\":\"天津科技有限公司\",\"position\":\"产品经理\",\"experience\":\"5年产品设计经验\",\"expectation\":\"希望在项目管理方面得到指导\"}"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 9.3 指导活动报名管理 (GuidanceRegistrationController)

**基础路径**: `/miniapp/haitang/registration`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询指导活动报名列表 | `/list` | `miniapp:haitang:registration:list` | 分页查询指导活动报名 |
| 导出指导活动报名 | `/export` | `miniapp:haitang:registration:export` | 导出Excel |
| 获取指导活动报名详情 | `/{registrationId}` | `miniapp:haitang:registration:query` | 根据ID获取指导活动报名 |
| 删除指导活动报名 | `/{registrationIds}` | `miniapp:haitang:registration:remove` | 批量删除指导活动报名 |

---

## 🌟 10. 西青金种子路演专区模块

### 10.1 专区活动管理 (XiqingActivityContentController)

**基础路径**: `/miniapp/xiqing/activity`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询专区活动内容列表 | `/list` | `miniapp:xiqing:activity:list` | 分页查询专区活动内容 |
| 获取专区活动内容详情 | `/{contentId}` | `miniapp:xiqing:activity:query` | 根据ID获取专区活动内容 |
| 修改专区活动内容 | `/` | `miniapp:xiqing:activity:edit` | 修改专区活动内容（富文本） |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取启用的专区活动内容 | `/app/getEnabledContent` | 获取当前启用的专区活动内容 |

### 10.2 路演报名管理 (XiqingRoadshowRegistrationController)

**基础路径**: `/miniapp/xiqing/registration`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询路演报名列表 | `/list` | `miniapp:xiqing:registration:list` | 分页查询路演报名 |
| 导出路演报名 | `/export` | `miniapp:xiqing:registration:export` | 导出Excel |
| 获取路演报名详情 | `/{registrationId}` | `miniapp:xiqing:registration:query` | 根据ID获取路演报名 |
| 删除路演报名 | `/{registrationIds}` | `miniapp:xiqing:registration:remove` | 批量删除路演报名 |
| 审核路演报名 | `/audit` | `miniapp:xiqing:registration:audit` | 审核路演报名 |
| 获取表单配置 | `/formConfig` | `miniapp:xiqing:registration:query` | 获取报名表单字段配置 |
| 更新表单配置 | `/formConfig` | `miniapp:xiqing:registration:edit` | 更新报名表单字段配置 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 提交路演报名 | `/app/register` | 小程序端提交路演报名资料（支持自定义字段） |

**路演报名参数示例**:
```json
{
  "userId": 123,
  "formData": "{\"name\":\"张三\",\"phone\":\"13800138000\",\"email\":\"<EMAIL>\",\"company\":\"天津科技有限公司\",\"projectName\":\"智能物联网项目\",\"projectDescription\":\"基于AI的智能物联网解决方案\",\"teamSize\":\"5人\",\"fundingNeeds\":\"100万元\"}"
}
```

### 10.3 报名咨询管理 (XiqingConsultationController)

**基础路径**: `/miniapp/xiqing/consultation`

#### 管理端接口

| 接口名称 | 接口地址 | 权限 | 描述 |
|---------|---------|------|------|
| 查询报名咨询列表 | `/list` | `miniapp:xiqing:consultation:list` | 分页查询报名咨询 |
| 导出报名咨询 | `/export` | `miniapp:xiqing:consultation:export` | 导出Excel |
| 获取报名咨询详情 | `/{consultationId}` | `miniapp:xiqing:consultation:query` | 根据ID获取报名咨询 |
| 修改报名咨询 | `/` | `miniapp:xiqing:consultation:edit` | 修改报名咨询信息 |
| 删除报名咨询 | `/{consultationIds}` | `miniapp:xiqing:consultation:remove` | 批量删除报名咨询 |

#### 小程序端接口

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 提交报名咨询 | `/app/submit` | 小程序端提交报名咨询 |

**报名咨询参数示例**:
```json
{
  "contactName": "李四",
  "contactMethod": "13900139000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 10.4 小程序API综合接口

**基础路径**: `/miniapp/api`

| 接口名称 | 接口地址 | 描述 |
|---------|---------|------|
| 获取西青金种子专区活动内容 | `/getXiqingActivityContent` | 获取专区活动内容（富文本） |

---

## 🎯 11. 核心业务流程

### 9.1 用户注册登录流程

1. **微信登录**: `/miniapp/user/app/weixinLogin`
2. **完善资料**: `/miniapp/user/completeBasicInfo` 等
3. **获取积分**: 完善资料自动获得积分

### 9.2 弹幕发布审核流程

1. **用户发布**: `/miniapp/barrage/app/publish`
2. **管理员审核**: `/miniapp/barrage/audit`
3. **前端展示**: `/miniapp/barrage/app/getApprovedList`

### 9.3 活动报名流程

1. **查看活动**: `/miniapp/event/app/getActiveList`
2. **检查报名状态**: `/miniapp/eventRegistration/app/checkRegistrationStatus`
3. **提交报名**: `/miniapp/eventRegistration/app/register`

### 10.4 项目报名流程

1. **小程序端提交**: `/miniapp/haitang/project/app/register`
2. **管理员审核**: `/miniapp/haitang/project/audit`
3. **查看报名列表**: `/miniapp/haitang/project/list`

### 10.5 指导活动报名流程

1. **查看指导活动**: `/miniapp/haitang/guidance/list`
2. **小程序端提交报名**: `/miniapp/haitang/guidance/app/register`
3. **管理员查看报名**: `/miniapp/haitang/registration/list`
4. **管理员管理报名**: `/miniapp/haitang/registration/{registrationId}`

---

## 📊 11. 性能优化建议

### 11.1 缓存策略

- 首页数据建议缓存 5 分钟
- 轮播图、通知等静态数据建议缓存 30 分钟
- 用户积分等动态数据实时查询

### 11.2 分页参数

所有列表接口支持分页，默认参数：
- `pageNum`: 页码（从1开始）
- `pageSize`: 每页数量（默认10）

### 11.3 响应优化

- 小程序端接口去除不必要的字段
- 使用综合API减少请求次数
- 合理使用数据库索引提升查询效率

---

## 🚀 12. 部署说明

### 12.1 环境要求

- Java 8+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.18+

### 12.2 接口测试

建议使用 Postman 或 Swagger UI 进行接口测试，所有接口都包含详细的注解说明。

### 12.3 错误处理

所有接口都包含统一的异常处理，返回标准的错误信息格式。

---

*本文档基于若依框架标准，所有接口均遵循RESTful设计原则，采用POST请求确保数据安全性。* 