// 小程序登录页面 JavaScript 示例
// 注意：实际使用时需要根据项目结构调整路径

Page({
  data: {
    loading: false
  },

  onLoad() {
    // 检查是否已经登录
    this.checkExistingLogin();
  },

  /**
   * 检查现有登录状态
   */
  checkExistingLogin() {
    const token = wx.getStorageSync('token');
    if (token) {
      // 已经有token，直接跳转到主页
      this.navigateToMain();
    }
  },

  /**
   * 快速登录（不需要手机号）
   */
  quickLogin() {
    this.setData({ loading: true });

    wx.login({
      success: (res) => {
        if (res.code) {
          this.performLogin(res.code);
        } else {
          this.showError('获取登录凭证失败');
        }
      },
      fail: () => {
        this.showError('微信登录失败');
      }
    });
  },

  /**
   * 手机号授权登录
   */
  onGetPhoneNumber(e) {
    console.log('手机号授权结果:', e.detail);
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      this.setData({ loading: true });
      
      // 获取新的登录凭证
      wx.login({
        success: (res) => {
          if (res.code) {
            this.performLoginWithPhone(res.code, e.detail);
          } else {
            this.showError('获取登录凭证失败');
          }
        },
        fail: () => {
          this.showError('微信登录失败');
        }
      });
    } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
      // 用户拒绝授权，提示可以使用快速登录
      wx.showModal({
        title: '提示',
        content: '您可以选择快速登录继续使用',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  /**
   * 执行普通登录
   */
  performLogin(code, additionalData = {}) {
    wx.request({
      url: this.getApiUrl('/miniapp/user/weixinLogin'),
      method: 'POST',
      data: {
        code,
        ...additionalData
      },
      success: (res) => {
        console.log('登录响应:', res.data);
        
        if (res.data.code === 200) {
          this.handleLoginSuccess(res.data.data);
        } else {
          this.showError(res.data.msg || '登录失败');
        }
      },
      fail: (error) => {
        console.error('登录请求失败:', error);
        this.showError('网络请求失败，请检查网络连接');
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 执行手机号登录
   */
  performLoginWithPhone(code, phoneDetail) {
    wx.request({
      url: this.getApiUrl('/miniapp/user/weixinLogin'),
      method: 'POST',
      data: {
        code,
        encryptedData: phoneDetail.encryptedData,
        iv: phoneDetail.iv
      },
      success: (res) => {
        console.log('手机号登录响应:', res.data);
        
        if (res.data.code === 200) {
          this.handleLoginSuccess(res.data.data, true);
        } else {
          this.showError(res.data.msg || '登录失败');
        }
      },
      fail: (error) => {
        console.error('手机号登录请求失败:', error);
        this.showError('网络请求失败，请检查网络连接');
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess(userData, hasPhone = false) {
    // 保存用户数据
    wx.setStorageSync('token', userData.token);
    wx.setStorageSync('userInfo', userData);

    // 显示欢迎信息
    const welcomeMsg = userData.isNewUser ? 
      (hasPhone ? '注册成功，欢迎加入！' : '欢迎新用户！') : 
      '欢迎回来！';
    
    wx.showToast({
      title: welcomeMsg,
      icon: 'success',
      duration: 2000
    });

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      this.navigateToMain();
    }, 2000);
  },

  /**
   * 跳转到主页
   */
  navigateToMain() {
    wx.switchTab({
      url: '/pages/index/index',
      fail: () => {
        // 如果不是tabBar页面，使用redirectTo
        wx.redirectTo({
          url: '/pages/index/index'
        });
      }
    });
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({ loading: false });
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 获取API地址
   */
  getApiUrl(path) {
    // 根据环境返回不同的API地址
    const baseUrl = 'https://your-api-domain.com'; // 替换为实际的API地址
    return baseUrl + path;
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/user-agreement'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/privacy-policy'
    });
  },

  /**
   * 智能登录（可选的高级功能）
   * 先检查用户状态，再决定登录流程
   */
  smartLogin() {
    this.setData({ loading: true });

    wx.login({
      success: (res) => {
        if (res.code) {
          // 先检查用户状态
          this.checkUserStatus(res.code)
            .then((statusData) => {
              const { isRegistered, needPhoneAuth } = statusData;
              
              if (isRegistered && !needPhoneAuth) {
                // 老用户且信息完整，直接登录
                this.performLogin(res.code);
              } else {
                // 新用户或需要手机号，显示选择对话框
                this.showLoginOptions(res.code);
              }
            })
            .catch(() => {
              // 检查失败，直接尝试登录
              this.performLogin(res.code);
            });
        } else {
          this.showError('获取登录凭证失败');
        }
      },
      fail: () => {
        this.showError('微信登录失败');
      }
    });
  },

  /**
   * 检查用户状态
   */
  checkUserStatus(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.getApiUrl('/miniapp/user/checkUserStatus'),
        method: 'POST',
        data: { code },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg));
          }
        },
        fail: reject
      });
    });
  },

  /**
   * 显示登录选项对话框
   */
  showLoginOptions(code) {
    this.setData({ loading: false });
    
    wx.showModal({
      title: '登录方式',
      content: '建议使用手机号登录以获得完整功能体验',
      confirmText: '手机号登录',
      cancelText: '快速登录',
      success: (res) => {
        if (res.confirm) {
          // 用户选择手机号登录，需要通过按钮触发
          wx.showToast({
            title: '请点击"手机号登录"按钮',
            icon: 'none'
          });
        } else {
          // 用户选择快速登录
          this.performLogin(code);
        }
      }
    });
  }
});
