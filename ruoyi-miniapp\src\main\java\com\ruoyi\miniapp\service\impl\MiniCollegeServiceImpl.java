package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniCollegeMapper;
import com.ruoyi.miniapp.domain.MiniCollege;
import com.ruoyi.miniapp.service.IMiniCollegeService;

/**
 * 学院信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class MiniCollegeServiceImpl implements IMiniCollegeService 
{
    @Autowired
    private MiniCollegeMapper miniCollegeMapper;

    /**
     * 查询学院信息
     * 
     * @param collegeId 学院信息主键
     * @return 学院信息
     */
    @Override
    public MiniCollege selectMiniCollegeByCollegeId(Long collegeId)
    {
        return miniCollegeMapper.selectMiniCollegeByCollegeId(collegeId);
    }

    /**
     * 查询学院信息列表
     * 
     * @param miniCollege 学院信息
     * @return 学院信息
     */
    @Override
    public List<MiniCollege> selectMiniCollegeList(MiniCollege miniCollege)
    {
        return miniCollegeMapper.selectMiniCollegeList(miniCollege);
    }

    /**
     * 新增学院信息
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    @Override
    public int insertMiniCollege(MiniCollege miniCollege)
    {
        miniCollege.setCreateTime(DateUtils.getNowDate());
        return miniCollegeMapper.insertMiniCollege(miniCollege);
    }

    /**
     * 修改学院信息
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    @Override
    public int updateMiniCollege(MiniCollege miniCollege)
    {
        miniCollege.setUpdateTime(DateUtils.getNowDate());
        return miniCollegeMapper.updateMiniCollege(miniCollege);
    }

    /**
     * 批量删除学院信息
     * 
     * @param collegeIds 需要删除的学院信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniCollegeByCollegeIds(Long[] collegeIds)
    {
        return miniCollegeMapper.deleteMiniCollegeByCollegeIds(collegeIds);
    }

    /**
     * 删除学院信息信息
     * 
     * @param collegeId 学院信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniCollegeByCollegeId(Long collegeId)
    {
        return miniCollegeMapper.deleteMiniCollegeByCollegeId(collegeId);
    }

    /**
     * 查询启用的学院信息列表
     * 
     * @return 学院信息集合
     */
    @Override
    public List<MiniCollege> selectEnabledMiniCollegeList()
    {
        return miniCollegeMapper.selectEnabledMiniCollegeList();
    }

    /**
     * 根据学院代码查询学院信息
     * 
     * @param collegeCode 学院代码
     * @return 学院信息
     */
    @Override
    public MiniCollege selectMiniCollegeByCollegeCode(String collegeCode)
    {
        return miniCollegeMapper.selectMiniCollegeByCollegeCode(collegeCode);
    }

    /**
     * 校验学院代码是否唯一
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    @Override
    public boolean checkCollegeCodeUnique(MiniCollege miniCollege)
    {
        Long collegeId = StringUtils.isNull(miniCollege.getCollegeId()) ? -1L : miniCollege.getCollegeId();
        MiniCollege info = miniCollegeMapper.selectMiniCollegeByCollegeCode(miniCollege.getCollegeCode());
        if (StringUtils.isNotNull(info) && info.getCollegeId().longValue() != collegeId.longValue())
        {
            return false;
        }
        return true;
    }
}
