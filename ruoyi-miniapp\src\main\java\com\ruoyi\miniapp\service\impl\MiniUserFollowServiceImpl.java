package com.ruoyi.miniapp.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.MiniUserFollowMapper;
import com.ruoyi.miniapp.domain.MiniUserFollow;
import com.ruoyi.miniapp.service.IMiniUserFollowService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 用户关注Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class MiniUserFollowServiceImpl implements IMiniUserFollowService 
{
    private static final Logger logger = LoggerFactory.getLogger(MiniUserFollowServiceImpl.class);

    @Autowired
    private MiniUserFollowMapper miniUserFollowMapper;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询用户关注
     * 
     * @param followId 用户关注主键
     * @return 用户关注
     */
    @Override
    public MiniUserFollow selectMiniUserFollowByFollowId(Long followId)
    {
        return miniUserFollowMapper.selectMiniUserFollowByFollowId(followId);
    }

    /**
     * 查询用户关注列表
     * 
     * @param miniUserFollow 用户关注
     * @return 用户关注
     */
    @Override
    public List<MiniUserFollow> selectMiniUserFollowList(MiniUserFollow miniUserFollow)
    {
        return miniUserFollowMapper.selectMiniUserFollowList(miniUserFollow);
    }

    /**
     * 新增用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    @Override
    public int insertMiniUserFollow(MiniUserFollow miniUserFollow)
    {
        return miniUserFollowMapper.insertMiniUserFollow(miniUserFollow);
    }

    /**
     * 修改用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    @Override
    public int updateMiniUserFollow(MiniUserFollow miniUserFollow)
    {
        return miniUserFollowMapper.updateMiniUserFollow(miniUserFollow);
    }

    /**
     * 批量删除用户关注
     * 
     * @param followIds 需要删除的用户关注主键
     * @return 结果
     */
    @Override
    public int deleteMiniUserFollowByFollowIds(Long[] followIds)
    {
        return miniUserFollowMapper.deleteMiniUserFollowByFollowIds(followIds);
    }

    /**
     * 删除用户关注信息
     * 
     * @param followId 用户关注主键
     * @return 结果
     */
    @Override
    public int deleteMiniUserFollowByFollowId(Long followId)
    {
        return miniUserFollowMapper.deleteMiniUserFollowByFollowId(followId);
    }

    /**
     * 关注用户
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean followUser(Long followerId, Long followedId)
    {
        try
        {
            // 检查参数
            if (followerId == null || followedId == null)
            {
                throw new ServiceException("用户ID不能为空");
            }

            // 不能关注自己
            if (followerId.equals(followedId))
            {
                throw new ServiceException("不能关注自己");
            }

            // 检查被关注用户是否存在
            if (userService.selectUserById(followedId) == null)
            {
                throw new ServiceException("被关注用户不存在");
            }

            // 检查是否已经关注
            MiniUserFollow existingFollow = miniUserFollowMapper.selectFollowRelation(followerId, followedId);
            if (existingFollow != null)
            {
                if ("0".equals(existingFollow.getStatus()))
                {
                    throw new ServiceException("已经关注过该用户");
                }
                else
                {
                    // 重新关注（更新状态）
                    int result = miniUserFollowMapper.updateFollowStatus(followerId, followedId, "0");
                    if (result > 0)
                    {
                        // 更新统计数据
                        updateUserFollowCounts(followerId);
                        updateUserFollowCounts(followedId);
                        logger.info("用户{}重新关注用户{}", followerId, followedId);
                        return true;
                    }
                    return false;
                }
            }

            // 创建新的关注记录
            MiniUserFollow follow = new MiniUserFollow();
            follow.setFollowerId(followerId);
            follow.setFollowedId(followedId);
            follow.setFollowTime(new Date());
            follow.setStatus("0");
            follow.setCreateBy(String.valueOf(followerId));
            follow.setCreateTime(new Date());

            int result = miniUserFollowMapper.insertMiniUserFollow(follow);
            if (result > 0)
            {
                // 更新统计数据
                updateUserFollowCounts(followerId);
                updateUserFollowCounts(followedId);
                logger.info("用户{}关注用户{}成功", followerId, followedId);
                return true;
            }
            return false;
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            logger.error("关注用户失败", e);
            throw new ServiceException("关注用户失败：" + e.getMessage());
        }
    }

    /**
     * 取消关注用户
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean unfollowUser(Long followerId, Long followedId)
    {
        try
        {
            // 检查参数
            if (followerId == null || followedId == null)
            {
                throw new ServiceException("用户ID不能为空");
            }

            // 检查是否已关注
            MiniUserFollow existingFollow = miniUserFollowMapper.selectFollowRelation(followerId, followedId);
            if (existingFollow == null || !"0".equals(existingFollow.getStatus()))
            {
                throw new ServiceException("未关注该用户");
            }

            // 更新关注状态为取消关注
            int result = miniUserFollowMapper.updateFollowStatus(followerId, followedId, "1");
            if (result > 0)
            {
                // 更新统计数据
                updateUserFollowCounts(followerId);
                updateUserFollowCounts(followedId);
                logger.info("用户{}取消关注用户{}成功", followerId, followedId);
                return true;
            }
            return false;
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            logger.error("取消关注用户失败", e);
            throw new ServiceException("取消关注用户失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否已关注某人
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 是否已关注
     */
    @Override
    public boolean isFollowing(Long followerId, Long followedId)
    {
        if (followerId == null || followedId == null)
        {
            return false;
        }

        MiniUserFollow follow = miniUserFollowMapper.selectFollowRelation(followerId, followedId);
        return follow != null && "0".equals(follow.getStatus());
    }

    /**
     * 获取用户的关注列表（我关注的人）
     * 
     * @param followerId 关注者ID
     * @return 关注列表
     */
    @Override
    public List<MiniUserFollow> getMyFollowingList(Long followerId)
    {
        return miniUserFollowMapper.selectMyFollowingList(followerId);
    }

    /**
     * 获取用户的粉丝列表（关注我的人）
     * 
     * @param followedId 被关注者ID
     * @return 粉丝列表
     */
    @Override
    public List<MiniUserFollow> getMyFollowersList(Long followedId)
    {
        return miniUserFollowMapper.selectMyFollowersList(followedId);
    }

    /**
     * 获取用户关注数量
     * 
     * @param userId 用户ID
     * @return 关注数量
     */
    @Override
    public int getFollowingCount(Long userId)
    {
        return miniUserFollowMapper.countFollowingByUserId(userId);
    }

    /**
     * 获取用户粉丝数量
     * 
     * @param userId 用户ID
     * @return 粉丝数量
     */
    @Override
    public int getFollowersCount(Long userId)
    {
        return miniUserFollowMapper.countFollowersByUserId(userId);
    }

    /**
     * 检查是否互相关注
     * 
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 是否互相关注
     */
    @Override
    public boolean isMutualFollow(Long userId1, Long userId2)
    {
        return miniUserFollowMapper.checkMutualFollow(userId1, userId2);
    }

    /**
     * 批量查询用户关注状态
     * 
     * @param followerId 关注者ID
     * @param followedIds 被关注者ID列表
     * @return 关注状态列表
     */
    @Override
    public List<MiniUserFollow> batchGetFollowStatus(Long followerId, List<Long> followedIds)
    {
        if (followerId == null || followedIds == null || followedIds.isEmpty())
        {
            return null;
        }
        return miniUserFollowMapper.batchSelectFollowStatus(followerId, followedIds);
    }

    /**
     * 更新用户关注统计数据
     * 
     * @param userId 用户ID
     */
    @Override
    @Transactional
    public void updateUserFollowCounts(Long userId)
    {
        try
        {
            int followingCount = getFollowingCount(userId);
            int followersCount = getFollowersCount(userId);

            // 更新用户表中的统计字段
            userService.updateUserFollowCounts(userId, followingCount, followersCount);
            
            logger.debug("更新用户{}关注统计：关注{}人，粉丝{}人", userId, followingCount, followersCount);
        }
        catch (Exception e)
        {
            logger.error("更新用户关注统计失败", e);
        }
    }
}
