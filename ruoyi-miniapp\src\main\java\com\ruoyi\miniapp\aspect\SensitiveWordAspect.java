package com.ruoyi.miniapp.aspect;

import java.lang.reflect.Field;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.miniapp.annotation.SensitiveWordFilter;
import com.ruoyi.miniapp.service.ISensitiveWordService;
import com.ruoyi.miniapp.service.ISensitiveWordLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 敏感词过滤切面
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Aspect
@Component
public class SensitiveWordAspect {
    
    private static final Logger log = LoggerFactory.getLogger(SensitiveWordAspect.class);
    
    @Autowired
    private ISensitiveWordService sensitiveWordService;
    
    @Autowired
    private ISensitiveWordLogService sensitiveWordLogService;
    
    @Around("@annotation(sensitiveWordFilter)")
    public Object around(ProceedingJoinPoint joinPoint, SensitiveWordFilter sensitiveWordFilter) throws Throwable {
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();

            // 处理每个参数
            for (Object arg : args) {
                if (arg != null) {
                    processSensitiveWord(arg, sensitiveWordFilter);
                }
            }

            // 继续执行原方法
            return joinPoint.proceed(args);
        } catch (ServiceException se) {
            // 对于业务异常（如敏感词拒绝），直接抛出，不受ignoreError影响
            log.warn("敏感词业务异常: {}", se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("敏感词过滤处理异常", e);
            // 如果过滤失败，根据配置决定是否继续执行
            if (sensitiveWordFilter.ignoreError()) {
                return joinPoint.proceed();
            } else {
                throw e;
            }
        }
    }
    
    /**
     * 处理对象中的敏感词
     */
    private void processSensitiveWord(Object obj, SensitiveWordFilter annotation) {
        if (obj == null) {
            return;
        }
        
        Class<?> clazz = obj.getClass();
        
        // 如果是基本类型或包装类型，直接返回
        if (clazz.isPrimitive() || isWrapperType(clazz)) {
            return;
        }
        
        // 处理字符串类型
        if (obj instanceof String) {
            return; // 字符串类型在参数级别处理
        }
        
        // 处理对象的字段
        Field[] fields = clazz.getDeclaredFields();
        String[] targetFields = annotation.fields();

        for (Field field : fields) {
            try {
                // 如果指定了要检测的字段，则只检测指定的字段
                if (targetFields.length > 0) {
                    boolean shouldCheck = false;
                    for (String targetField : targetFields) {
                        if (field.getName().equals(targetField)) {
                            shouldCheck = true;
                            break;
                        }
                    }
                    if (!shouldCheck) {
                        continue; // 跳过不需要检测的字段
                    }
                }

                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                if (fieldValue instanceof String) {
                    String content = (String) fieldValue;
                    if (StringUtils.isNotEmpty(content)) {
                        String filteredContent = processStringContent(content, annotation);
                        if (!content.equals(filteredContent)) {
                            field.set(obj, filteredContent);
                        }
                    }
                }
            } catch (ServiceException se) {
                // 敏感词业务异常需要向上抛出，不能被吞掉
                log.warn("字段 {} 包含敏感词: {}", field.getName(), se.getMessage());
                throw se;
            } catch (Exception e) {
                log.error("处理字段敏感词失败: " + field.getName(), e);
            }
        }
    }
    
    /**
     * 处理字符串内容
     */
    private String processStringContent(String content, SensitiveWordFilter annotation) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }

        // 检查是否包含敏感词
        boolean containsSensitive = sensitiveWordService.containsSensitiveWord(content);
        if (!containsSensitive) {
            return content;
        }

        // 查找命中的敏感词
        List<String> hitWords = sensitiveWordService.findSensitiveWords(content);

        // 根据处理策略进行处理
        switch (annotation.strategy()) {
            case REPLACE:
                String replacedContent = sensitiveWordService.replaceSensitiveWords(content);
                // 记录替换日志
                recordSensitiveWordLog(content, replacedContent, annotation.moduleName(), "替换", hitWords);
                log.info("敏感词已替换 - 模块: {}, 原内容长度: {}, 命中敏感词: {}",
                        annotation.moduleName(), content.length(), hitWords);
                return replacedContent;

            case REJECT:
                // 记录拒绝日志
                recordSensitiveWordLog(content, content, annotation.moduleName(), "拒绝", hitWords);
                log.warn("敏感词检测拒绝操作 - 模块: {}, 内容长度: {}, 命中敏感词: {}",
                        annotation.moduleName(), content.length(), hitWords);
                // 抛出业务异常而不是运行时异常
                throw new ServiceException("内容包含敏感词，请修改后重试。命中词汇: " + String.join(", ", hitWords));

            case LOG_ONLY:
                // 仅记录日志，不修改内容
                recordSensitiveWordLog(content, content, annotation.moduleName(), "记录", hitWords);
                log.info("敏感词仅记录 - 模块: {}, 内容长度: {}, 命中敏感词: {}",
                        annotation.moduleName(), content.length(), hitWords);
                return content;

            default:
                return content;
        }
    }
    
    /**
     * 记录敏感词日志
     */
    private void recordSensitiveWordLog(String originalContent, String filteredContent, String moduleName, String action, List<String> hitWords) {
        try {
            // 获取当前用户信息
            Long userId = null;
            String userName = null;
            try {
                userId = SecurityUtils.getUserId();
                userName = SecurityUtils.getUsername();
            } catch (Exception e) {
                // 忽略获取用户信息失败的异常
            }
            
            // 获取请求信息
            String clientIp = "";
            String userAgent = "";
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    clientIp = IpUtils.getIpAddr(request);
                    userAgent = request.getHeader("User-Agent");
                }
            } catch (Exception e) {
                // 忽略获取请求信息失败的异常
            }
            
            // 记录日志
            sensitiveWordLogService.recordSensitiveWordLog(
                userId, userName, moduleName, action,
                originalContent, filteredContent, hitWords,
                clientIp, userAgent
            );

            // 更新敏感词命中次数
            if (hitWords != null && !hitWords.isEmpty()) {
                sensitiveWordService.updateHitCount(hitWords);
            }
        } catch (Exception e) {
            log.error("记录敏感词日志失败", e);
        }
    }
    
    /**
     * 判断是否为包装类型
     */
    private boolean isWrapperType(Class<?> clazz) {
        return clazz == Boolean.class || clazz == Character.class || clazz == Byte.class ||
               clazz == Short.class || clazz == Integer.class || clazz == Long.class ||
               clazz == Float.class || clazz == Double.class;
    }
}
