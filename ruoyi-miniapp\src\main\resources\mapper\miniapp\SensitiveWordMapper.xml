<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.SensitiveWordMapper">
    
    <resultMap type="SensitiveWord" id="SensitiveWordResult">
        <result property="wordId"    column="word_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="wordContent"    column="word_content"    />
        <result property="wordType"    column="word_type"    />
        <result property="severityLevel"    column="severity_level"    />
        <result property="replacementChar"    column="replacement_char"    />
        <result property="isRegex"    column="is_regex"    />
        <result property="hitCount"    column="hit_count"    />
        <result property="lastHitTime"    column="last_hit_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSensitiveWordVo">
        select sw.word_id, sw.category_id, swc.category_name, sw.word_content, sw.word_type, sw.severity_level, 
               sw.replacement_char, sw.is_regex, sw.hit_count, sw.last_hit_time, sw.status, 
               sw.create_by, sw.create_time, sw.update_by, sw.update_time, sw.remark 
        from sensitive_word sw
        left join sensitive_word_category swc on sw.category_id = swc.category_id
    </sql>

    <select id="selectSensitiveWordList" parameterType="SensitiveWord" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        <where>  
            <if test="categoryId != null "> and sw.category_id = #{categoryId}</if>
            <if test="wordContent != null  and wordContent != ''"> and sw.word_content like concat('%', #{wordContent}, '%')</if>
            <if test="wordType != null  and wordType != ''"> and sw.word_type = #{wordType}</if>
            <if test="severityLevel != null  and severityLevel != ''"> and sw.severity_level = #{severityLevel}</if>
            <if test="status != null  and status != ''"> and sw.status = #{status}</if>
        </where>
        order by sw.hit_count desc, sw.create_time desc
    </select>
    
    <select id="selectSensitiveWordByWordId" parameterType="Long" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.word_id = #{wordId}
    </select>

    <select id="selectEnabledSensitiveWordList" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.status = '0'
        order by sw.severity_level desc, sw.hit_count desc
    </select>

    <select id="selectSensitiveWordByType" parameterType="String" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.word_type = #{wordType} and sw.status = '0'
        order by sw.severity_level desc, sw.hit_count desc
    </select>

    <select id="selectSensitiveWordByCategoryId" parameterType="Long" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.category_id = #{categoryId} and sw.status = '0'
        order by sw.severity_level desc, sw.hit_count desc
    </select>

    <select id="selectSensitiveWordByContent" parameterType="String" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.word_content = #{wordContent}
    </select>

    <select id="checkWordContentUnique" parameterType="map" resultType="int">
        select count(1) from sensitive_word 
        where word_content = #{wordContent}
        <if test="wordId != null">
            and word_id != #{wordId}
        </if>
    </select>
        
    <insert id="insertSensitiveWord" parameterType="SensitiveWord" useGeneratedKeys="true" keyProperty="wordId">
        insert into sensitive_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="wordContent != null and wordContent != ''">word_content,</if>
            <if test="wordType != null">word_type,</if>
            <if test="severityLevel != null">severity_level,</if>
            <if test="replacementChar != null">replacement_char,</if>
            <if test="isRegex != null">is_regex,</if>
            <if test="hitCount != null">hit_count,</if>
            <if test="lastHitTime != null">last_hit_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="wordContent != null and wordContent != ''">#{wordContent},</if>
            <if test="wordType != null">#{wordType},</if>
            <if test="severityLevel != null">#{severityLevel},</if>
            <if test="replacementChar != null">#{replacementChar},</if>
            <if test="isRegex != null">#{isRegex},</if>
            <if test="hitCount != null">#{hitCount},</if>
            <if test="lastHitTime != null">#{lastHitTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSensitiveWord" parameterType="SensitiveWord">
        update sensitive_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="wordContent != null and wordContent != ''">word_content = #{wordContent},</if>
            <if test="wordType != null">word_type = #{wordType},</if>
            <if test="severityLevel != null">severity_level = #{severityLevel},</if>
            <if test="replacementChar != null">replacement_char = #{replacementChar},</if>
            <if test="isRegex != null">is_regex = #{isRegex},</if>
            <if test="hitCount != null">hit_count = #{hitCount},</if>
            <if test="lastHitTime != null">last_hit_time = #{lastHitTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where word_id = #{wordId}
    </update>

    <update id="updateSensitiveWordHitCount" parameterType="String">
        update sensitive_word 
        set hit_count = hit_count + 1, last_hit_time = now()
        where word_content = #{wordContent} and status = '0'
    </update>

    <update id="batchUpdateSensitiveWordHitCount" parameterType="map">
        update sensitive_word 
        set hit_count = hit_count + 1, last_hit_time = now()
        where word_content in 
        <foreach item="wordContent" collection="wordContents" open="(" separator="," close=")">
            #{wordContent}
        </foreach>
        and status = '0'
    </update>

    <select id="selectSensitiveWordByWordIds" parameterType="String" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where sw.word_id in
        <foreach item="wordId" collection="array" open="(" separator="," close=")">
            #{wordId}
        </foreach>
    </select>

    <delete id="deleteSensitiveWordByWordId" parameterType="Long">
        delete from sensitive_word where word_id = #{wordId}
    </delete>

    <delete id="deleteSensitiveWordByWordIds" parameterType="String">
        delete from sensitive_word where word_id in 
        <foreach item="wordId" collection="array" open="(" separator="," close=")">
            #{wordId}
        </foreach>
    </delete>

</mapper>
