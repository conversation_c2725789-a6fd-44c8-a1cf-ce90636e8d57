//package com.ruoyi.miniapp.exception;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.core.annotation.Order;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.exception.ServiceException;
//
///**
// * 敏感词异常处理器
// *
// * <AUTHOR>
// * @date 2025-01-17
// */
//@RestControllerAdvice(basePackages = "com.ruoyi.miniapp")
////@Order(1)  // 设置高优先级，确保在全局异常处理器之前执行
//public class SensitiveWordExceptionHandler {
//
//    private static final Logger log = LoggerFactory.getLogger(SensitiveWordExceptionHandler.class);
//
//    /**
//     * 处理敏感词相关的业务异常
//     */
//    @ExceptionHandler(ServiceException.class)
//    public AjaxResult handleSensitiveWordException(ServiceException e) {
//        String message = e.getMessage();
//
//        // 判断是否为敏感词相关异常
//        if (message != null && message.contains("敏感词")) {
//            log.warn("敏感词检测拦截: {}", message);
//
//            return AjaxResult.error(460, message)
//                    .put("type", "SENSITIVE_WORD")
//                    .put("action", "CONTENT_REJECTED");
//        }
//
//        // 其他业务异常正常处理
//        return AjaxResult.error(e.getMessage());
//    }
//}
