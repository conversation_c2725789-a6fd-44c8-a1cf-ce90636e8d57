package com.ruoyi.miniapp.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.XiqingConsultation;
import com.ruoyi.miniapp.service.IXiqingConsultationService;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 西青金种子报名咨询Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "西青金种子-报名咨询")
@RestController
@RequestMapping("/miniapp/xiqing/consultation")
public class XiqingConsultationController extends BaseController
{
    @Autowired
    private IXiqingConsultationService xiqingConsultationService;

    /**
     * 查询西青金种子报名咨询列表
     */
    @ApiOperation("查询报名咨询列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:consultation:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") XiqingConsultation xiqingConsultation)
    {
        startPage();
        List<XiqingConsultation> list = xiqingConsultationService.selectXiqingConsultationList(xiqingConsultation);
        return getDataTable(list);
    }

    /**
     * 获取西青金种子报名咨询详细信息
     */
    @ApiOperation("获取报名咨询详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:consultation:query')")
    @GetMapping(value = "/{consultationId}")
    public AjaxResult getInfo(@ApiParam("咨询ID") @PathVariable("consultationId") Long consultationId)
    {
        return AjaxResult.success(xiqingConsultationService.selectXiqingConsultationByConsultationId(consultationId));
    }

    /**
     * 修改西青金种子报名咨询
     */
    @ApiOperation("修改报名咨询")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:consultation:edit')")
    @Log(title = "西青金种子报名咨询", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("咨询信息") @RequestBody XiqingConsultation xiqingConsultation)
    {
        // 如果consultationId为null，则新增；否则更新
        if (xiqingConsultation.getConsultationId() == null) {
            return toAjax(xiqingConsultationService.insertXiqingConsultation(xiqingConsultation));
        } else {
            return toAjax(xiqingConsultationService.updateXiqingConsultation(xiqingConsultation));
        }
    }

    /**
     * 获取报名咨询联系信息（用于编辑）
     */
    @ApiOperation("获取报名咨询联系信息")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:consultation:query')")
    @GetMapping("/getContactInfo")
    public AjaxResult getContactInfo()
    {
        // 获取最新的一条咨询记录作为联系信息
        XiqingConsultation queryParam = new XiqingConsultation();
        queryParam.setStatus("0"); // 只查询正常状态的
        List<XiqingConsultation> list = xiqingConsultationService.selectXiqingConsultationList(queryParam);

        if (list != null && !list.isEmpty()) {
            XiqingConsultation contactInfo = list.get(0);
            // 只返回联系人和联系方式
            XiqingConsultation result = new XiqingConsultation();
            result.setConsultationId(contactInfo.getConsultationId());
            result.setContactName(contactInfo.getContactName());
            result.setContactMethod(contactInfo.getContactMethod());
            result.setStatus(contactInfo.getStatus());
            result.setRemark(contactInfo.getRemark());
            return AjaxResult.success(result);
        } else {
            // 如果没有记录，返回空的联系信息供编辑
            XiqingConsultation emptyContact = new XiqingConsultation();
            emptyContact.setContactName("");
            emptyContact.setContactMethod("");
            emptyContact.setStatus("0");
            return AjaxResult.success(emptyContact);
        }
    }



    // ==================== 小程序端接口 ====================

    /**
     * 提交报名咨询（小程序端）
     */
    @ApiOperation("提交报名咨询")
    @PostMapping("/app/submit")
    public AjaxResult submit(@ApiParam("咨询信息") @RequestBody XiqingConsultation xiqingConsultation)
    {
        xiqingConsultation.setStatus("0"); // 正常状态
        return toAjax(xiqingConsultationService.insertXiqingConsultation(xiqingConsultation));
    }
}
