package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniPageContent;
import com.ruoyi.miniapp.service.IMiniPageContentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 页面内容管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "页面内容管理")
@RestController
@RequestMapping("/miniapp/content/page")
public class MiniPageContentController extends BaseController
{
    @Autowired
    private IMiniPageContentService miniPageContentService;

    /**
     * 查询页面内容管理列表
     */
    @ApiOperation("查询页面内容管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") MiniPageContent miniPageContent)
    {
        startPage();
        List<MiniPageContent> list = miniPageContentService.selectMiniPageContentList(miniPageContent);
        return getDataTable(list);
    }

    /**
     * 导出页面内容管理列表
     */
    @ApiOperation("导出页面内容管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:export')")
    @Log(title = "页面内容管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") MiniPageContent miniPageContent)
    {
        List<MiniPageContent> list = miniPageContentService.selectMiniPageContentList(miniPageContent);
        ExcelUtil<MiniPageContent> util = new ExcelUtil<MiniPageContent>(MiniPageContent.class);
        util.exportExcel(response, list, "页面内容管理数据");
    }

    /**
     * 获取页面内容管理详细信息
     */
    @ApiOperation("获取页面内容管理详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:query')")
    @GetMapping(value = "/{contentId}")
    public AjaxResult getInfo(@ApiParam("内容ID") @PathVariable("contentId") Long contentId)
    {
        return AjaxResult.success(miniPageContentService.selectMiniPageContentByContentId(contentId));
    }

    /**
     * 新增页面内容管理
     */
    @ApiOperation("新增页面内容管理")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:add')")
    @Log(title = "页面内容管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("页面内容信息") @RequestBody MiniPageContent miniPageContent)
    {
        return toAjax(miniPageContentService.insertMiniPageContent(miniPageContent));
    }

    /**
     * 修改页面内容管理
     */
    @ApiOperation("修改页面内容管理")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:edit')")
    @Log(title = "页面内容管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("页面内容信息") @RequestBody MiniPageContent miniPageContent)
    {
        return toAjax(miniPageContentService.updateMiniPageContent(miniPageContent));
    }

    /**
     * 删除页面内容管理
     */
    @ApiOperation("删除页面内容管理")
    @PreAuthorize("@ss.hasPermi('miniapp:content:page:remove')")
    @Log(title = "页面内容管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{contentIds}")
    public AjaxResult remove(@ApiParam("内容ID数组") @PathVariable Long[] contentIds)
    {
        return toAjax(miniPageContentService.deleteMiniPageContentByContentIds(contentIds));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 根据页面编码获取页面内容（小程序端）
     */
    @ApiOperation("根据页面编码获取页面内容")
    @GetMapping("/app/getByCode/{pageCode}")
    public AjaxResult getByPageCode(@ApiParam("页面编码") @PathVariable("pageCode") String pageCode)
    {
        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageCode(pageCode);
        if (content == null) {
            return AjaxResult.error("页面内容不存在");
        }
        return AjaxResult.success(content);
    }

    /**
     * 获取加入我们页面内容（小程序端）
     */
    @ApiOperation("获取加入我们页面内容")
    @GetMapping("/app/getJoinUs")
    public AjaxResult getJoinUs()
    {
        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageCode("join_us");
        if (content == null) {
            return AjaxResult.error("加入我们页面内容不存在");
        }
        return AjaxResult.success(content);
    }

    /**
     * 获取关于我们页面内容（小程序端）
     */
    @ApiOperation("获取关于我们页面内容")
    @GetMapping("/app/getAboutUs")
    public AjaxResult getAboutUs()
    {
        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageCode("about_us");
        if (content == null) {
            return AjaxResult.error("关于我们页面内容不存在");
        }
        return AjaxResult.success(content);
    }

    /**
     * 获取用户协议内容（小程序端）
     */
    @Anonymous
    @ApiOperation("获取用户协议内容")
    @GetMapping("/app/getUserAgreement")
    public AjaxResult getUserAgreement()
    {
        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageCode("user_agreement");
        if (content == null) {
            return AjaxResult.error("用户协议内容不存在");
        }
        return AjaxResult.success(content);
    }

    /**
     * 获取隐私协议内容（小程序端）
     */
    @Anonymous
    @ApiOperation("获取隐私协议内容")
    @GetMapping("/app/getPrivacyPolicy")
    public AjaxResult getPrivacyPolicy()
    {
        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageCode("privacy_policy");
        if (content == null) {
            return AjaxResult.error("隐私协议内容不存在");
        }
        return AjaxResult.success(content);
    }
}
