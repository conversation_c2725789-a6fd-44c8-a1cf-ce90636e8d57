<template>
  <div class="app-container">
    <!-- 表单配置管理区域 -->
    <div class="form-config-container">
      <div class="config-header">
        <h3>项目报名表单配置管理</h3>
        <div class="header-actions">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            v-hasPermi="['miniapp:haitang:formConfig:add']"
          >新增配置</el-button>
        </div>
      </div>

      <div class="config-content" v-loading="loading">
        <!-- 当前启用的配置 -->
        <div v-if="enabledConfig" class="enabled-config">
          <div class="enabled-header">
            <h4>
              <i class="el-icon-check" style="color: #67c23a;"></i>
              当前启用配置：{{ enabledConfig.configName }}
            </h4>
            <div class="enabled-actions">
              <el-button
                size="small"
                type="primary"
                icon="el-icon-setting"
                @click="handleFormConfig(enabledConfig)"
                v-hasPermi="['miniapp:haitang:formConfig:edit']"
              >配置表单</el-button>
              <el-button
                size="small"
                type="success"
                icon="el-icon-view"
                @click="handlePreview(enabledConfig)"
              >预览表单</el-button>
            </div>
          </div>
          <div class="enabled-description">
            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>
          </div>
          <div v-if="enabledFormFields.length > 0" class="form-preview">
            <h5>表单字段预览：</h5>
            <div class="field-list">
              <div v-for="(field, index) in enabledFormFields" :key="index" class="field-item">
                <div class="field-info">
                  <i :class="getFieldIcon(field.type)" class="field-icon"></i>
                  <span class="field-label">{{ field.label }}</span>
                  <el-tag size="mini" type="success">{{ field.name }}</el-tag>
                  <el-tag v-if="field.required" size="mini" type="danger">必填</el-tag>
                  <el-tag v-else size="mini" type="info">选填</el-tag>
                  <span class="field-type">{{ getFieldTypeName(field.type) }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-form">
            <i class="el-icon-document-add"></i>
            <p>该配置暂未设置表单字段，点击"配置表单"开始设置</p>
          </div>
        </div>

        <!-- 无启用配置时的提示 -->
        <div v-else class="no-enabled-config">
          <i class="el-icon-warning-outline"></i>
          <p>暂无启用的表单配置，请先创建并启用一个配置</p>
        </div>
      </div>
    </div>

    <!-- 所有配置列表 -->
    <div class="config-list-container">
      <div class="list-header">
        <h4>所有表单配置</h4>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
          <el-form-item label="配置名称" prop="configName">
            <el-input
              v-model="queryParams.configName"
              placeholder="请输入配置名称"
              clearable
              style="width: 200px;"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table v-loading="listLoading" :data="formConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="配置名称" align="center" prop="configName" min-width="150" />
        <el-table-column label="配置描述" align="center" prop="configDescription" show-overflow-tooltip min-width="200" />
        <el-table-column label="是否启用" align="center" prop="isEnabled" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnabled === '1'" type="success">已启用</el-tag>
            <el-tag v-else type="info">未启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-setting"
              @click="handleFormConfig(scope.row)"
              v-hasPermi="['miniapp:haitang:formConfig:edit']"
            >配置</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['miniapp:haitang:formConfig:edit']"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleEnable(scope.row)"
              v-hasPermi="['miniapp:haitang:formConfig:edit']"
              v-if="scope.row.isEnabled !== '1'"
            >启用</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['miniapp:haitang:formConfig:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改配置基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置描述" prop="configDescription">
          <el-input v-model="form.configDescription" type="textarea" :rows="3" placeholder="请输入配置描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 表单配置对话框 -->
    <el-dialog title="表单字段配置" :visible.sync="formConfigOpen" width="1000px" append-to-body>
      <div class="form-fields-config">
        <!-- 工具栏 -->
        <div class="form-fields-toolbar">
          <div class="toolbar-left">
            <el-button type="primary" size="small" @click="addFormField" icon="el-icon-plus">
              添加字段
            </el-button>
            <el-dropdown @command="handleTemplateCommand" size="small">
              <el-button size="small">
                预设模板<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="basic">基础信息模板</el-dropdown-item>
                <el-dropdown-item command="project">项目报名模板</el-dropdown-item>
                <el-dropdown-item command="advanced">高级字段模板</el-dropdown-item>
                <el-dropdown-item command="clear">清空所有字段</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="handlePreviewForm" icon="el-icon-view">预览表单</el-button>
          </div>
        </div>

        <!-- 字段配置列表 -->
        <div class="form-fields-list">
          <div v-if="currentFormFields.length === 0" class="empty-fields">
            <i class="el-icon-document-add"></i>
            <p>暂无字段，点击"添加字段"开始配置</p>
          </div>
          <div v-else>
            <div v-for="(field, index) in currentFormFields" :key="index" class="field-config-item">
              <div class="field-config-header">
                <span class="field-index">{{ index + 1 }}</span>
                <el-input v-model="field.label" placeholder="字段标签" size="small" style="width: 150px;" />
                <el-input v-model="field.name" placeholder="字段名称" size="small" style="width: 120px;" />
                <el-select v-model="field.type" placeholder="字段类型" size="small" style="width: 140px;">
                  <el-option label="📝 文本输入" value="input" />
                  <el-option label="📄 多行文本" value="textarea" />
                  <el-option label="🔢 数字输入" value="number" />
                  <el-option label="📧 邮箱" value="email" />
                  <el-option label="📞 电话" value="tel" />
                  <el-option label="🔘 单选" value="radio" />
                  <el-option label="☑️ 多选" value="checkbox" />
                  <el-option label="📋 下拉选择" value="select" />
                  <el-option label="🔘➕ 单选+其他" value="radio_other" />
                  <el-option label="☑️➕ 多选+其他" value="checkbox_other" />
                  <el-option label="📋➕ 下拉+其他" value="select_other" />
                  <el-option label="📅 日期" value="date" />
                  <el-option label="⏰ 时间" value="time" />
                  <el-option label="📅⏰ 日期时间" value="datetime" />
                  <el-option label="📎 文件上传" value="file" />
                  <el-option label="🔗 网址链接" value="url" />
                  <el-option label="🏢 身份证号" value="idcard" />
                  <el-option label="💰 金额" value="money" />
                  <el-option label="📏 评分" value="rate" />
                  <el-option label="🎚️ 滑块" value="slider" />
                  <el-option label="🔄 开关" value="switch" />
                  <el-option label="🌈 颜色选择" value="color" />
                </el-select>
                <el-checkbox v-model="field.required" size="small">必填</el-checkbox>
                <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeFormField(index)">删除</el-button>
              </div>
              <div class="field-config-body" v-if="needOptions(field.type)">
                <el-input
                  v-model="field.options"
                  :placeholder="getOptionsPlaceholder(field.type)"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveFormConfig">保存配置</el-button>
        <el-button @click="formConfigOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="previewDialogVisible" width="600px" append-to-body>
      <div class="form-preview">
        <div class="preview-header">
          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>
          <p>请填写以下信息完成报名</p>
        </div>
        <div class="preview-form">
          <div v-for="(field, index) in currentFormFields" :key="index" class="preview-field">
            <label class="preview-label">
              {{ field.label }}
              <span v-if="field.required" class="required">*</span>
            </label>
            <div class="preview-input">
              <!-- 基础输入类型 -->
              <el-input
                v-if="['input', 'email', 'tel', 'url', 'idcard'].includes(field.type)"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
              />
              <el-input
                v-else-if="field.type === 'textarea'"
                type="textarea"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
              />
              <!-- 数字类型 -->
              <el-input-number
                v-else-if="['number', 'money'].includes(field.type)"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
              />
              <!-- 单选类型 -->
              <el-radio-group v-else-if="field.type === 'radio'" disabled>
                <el-radio
                  v-for="option in getFieldOptions(field.options)"
                  :key="option"
                  :label="option"
                >{{ option }}</el-radio>
              </el-radio-group>
              <!-- 单选+其他 -->
              <div v-else-if="field.type === 'radio_other'">
                <el-radio-group disabled style="margin-bottom: 8px;">
                  <el-radio
                    v-for="option in getFieldOptions(field.options)"
                    :key="option"
                    :label="option"
                  >{{ option }}</el-radio>
                  <el-radio label="其他">其他</el-radio>
                </el-radio-group>
                <el-input placeholder="选择'其他'时请在此输入具体内容" size="small" disabled />
              </div>
              <!-- 多选类型 -->
              <el-checkbox-group v-else-if="field.type === 'checkbox'" disabled>
                <el-checkbox
                  v-for="option in getFieldOptions(field.options)"
                  :key="option"
                  :label="option"
                >{{ option }}</el-checkbox>
              </el-checkbox-group>
              <!-- 多选+其他 -->
              <div v-else-if="field.type === 'checkbox_other'">
                <el-checkbox-group disabled style="margin-bottom: 8px;">
                  <el-checkbox
                    v-for="option in getFieldOptions(field.options)"
                    :key="option"
                    :label="option"
                  >{{ option }}</el-checkbox>
                  <el-checkbox label="其他">其他</el-checkbox>
                </el-checkbox-group>
                <el-input placeholder="选择'其他'时请在此输入具体内容" size="small" disabled />
              </div>
              <!-- 下拉选择 -->
              <el-select v-else-if="field.type === 'select'" :placeholder="'请选择' + field.label" size="small" disabled>
                <el-option
                  v-for="option in getFieldOptions(field.options)"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>
              <!-- 下拉+其他 -->
              <div v-else-if="field.type === 'select_other'">
                <el-select :placeholder="'请选择' + field.label" size="small" disabled style="margin-bottom: 8px; width: 100%;">
                  <el-option
                    v-for="option in getFieldOptions(field.options)"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                  <el-option label="其他" value="其他" />
                </el-select>
                <el-input placeholder="选择'其他'时请在此输入具体内容" size="small" disabled />
              </div>
              <!-- 日期时间类型 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                type="date"
                :placeholder="'请选择' + field.label"
                size="small"
                disabled
              />
              <el-time-picker
                v-else-if="field.type === 'time'"
                :placeholder="'请选择' + field.label"
                size="small"
                disabled
              />
              <el-date-picker
                v-else-if="field.type === 'datetime'"
                type="datetime"
                :placeholder="'请选择' + field.label"
                size="small"
                disabled
              />
              <!-- 评分 -->
              <el-rate
                v-else-if="field.type === 'rate'"
                disabled
                :max="getFieldOptions(field.options).length || 5"
              />
              <!-- 滑块 -->
              <el-slider
                v-else-if="field.type === 'slider'"
                disabled
                :max="100"
                style="margin: 12px 0;"
              />
              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                disabled
              />
              <!-- 颜色选择 -->
              <el-color-picker
                v-else-if="field.type === 'color'"
                disabled
              />
              <!-- 文件上传 -->
              <el-upload
                v-else-if="field.type === 'file'"
                class="upload-demo"
                action="#"
                :auto-upload="false"
                disabled
              >
                <el-button size="small" type="primary" disabled>点击上传</el-button>
              </el-upload>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig } from "@/api/miniapp/haitang/formConfig";
import request from '@/utils/request';

export default {
  name: "FormConfig",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      listLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 天大海棠杯项目报名表单配置表格数据
      formConfigList: [],
      // 当前启用的配置
      enabledConfig: null,
      // 启用配置的表单字段
      enabledFormFields: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示表单配置弹出层
      formConfigOpen: false,
      // 是否显示表单预览弹出层
      previewDialogVisible: false,
      // 当前配置的表单字段
      currentFormFields: [],
      // 当前操作的配置
      currentConfig: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.loadEnabledConfig();
    this.getList();
  },
  methods: {
    /** 加载启用的配置 */
    loadEnabledConfig() {
      this.loading = true;
      request({
        url: '/miniapp/haitang/formConfig/enabled',
        method: 'get'
      }).then(response => {
        if (response.data) {
          this.enabledConfig = response.data;
          this.loadEnabledFormFields();
        } else {
          this.enabledConfig = null;
          this.enabledFormFields = [];
        }
        this.loading = false;
      }).catch(() => {
        this.enabledConfig = null;
        this.enabledFormFields = [];
        this.loading = false;
      });
    },
    /** 加载启用配置的表单字段 */
    loadEnabledFormFields() {
      if (this.enabledConfig && this.enabledConfig.formConfig) {
        try {
          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);
        } catch (e) {
          this.enabledFormFields = [];
        }
      } else {
        this.enabledFormFields = [];
      }
    },
    /** 查询天大海棠杯项目报名表单配置列表 */
    getList() {
      this.listLoading = true;
      listFormConfig(this.queryParams).then(response => {
        this.formConfigList = response.rows;
        this.total = response.total;
        this.listLoading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        configName: null,
        configDescription: null,
        formConfig: null,
        isEnabled: "0",
        sortOrder: 0,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加天大海棠杯项目报名表单配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids
      getFormConfig(configId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改天大海棠杯项目报名表单配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != null) {
            updateFormConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFormConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为"' + configIds + '"的数据项？').then(function() {
        return delFormConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 表单配置按钮操作 */
    handleFormConfig(row) {
      this.currentConfig = row;
      this.currentFormFields = [];
      if (row.formConfig) {
        try {
          this.currentFormFields = JSON.parse(row.formConfig);
        } catch (e) {
          this.currentFormFields = [];
        }
      }
      this.formConfigOpen = true;
    },
    /** 预览按钮操作 */
    handlePreview(row) {
      this.currentConfig = row;
      this.currentFormFields = [];
      if (row.formConfig) {
        try {
          this.currentFormFields = JSON.parse(row.formConfig);
        } catch (e) {
          this.currentFormFields = [];
        }
      }
      this.previewDialogVisible = true;
    },
    /** 启用按钮操作 */
    handleEnable(row) {
      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {
        return enableFormConfig(row.configId);
      }).then(() => {
        this.loadEnabledConfig();
        this.getList();
        this.$modal.msgSuccess("启用成功");
      }).catch(() => {});
    },
    /** 添加表单字段 */
    addFormField() {
      const defaultName = this.generateUniqueFieldName('field');
      this.currentFormFields.push({
        name: defaultName,
        label: '',
        type: 'input',
        required: false,
        options: ''
      });
    },
    /** 删除表单字段 */
    removeFormField(index) {
      this.currentFormFields.splice(index, 1);
    },
    /** 生成唯一字段名 */
    generateUniqueFieldName(prefix) {
      let counter = 1;
      let name = prefix + counter;
      while (this.currentFormFields.some(field => field.name === name)) {
        counter++;
        name = prefix + counter;
      }
      return name;
    },
    /** 判断字段类型是否需要选项 */
    needOptions(type) {
      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'rate'].includes(type);
    },
    /** 处理模板命令 */
    handleTemplateCommand(command) {
      if (command === 'clear') {
        this.$modal.confirm('确认清空所有字段？').then(() => {
          this.currentFormFields = [];
        });
        return;
      }

      const templates = {
        basic: [
          { label: '姓名', name: '', type: 'input', required: true, options: '' },
          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' },
          { label: '身份证号', name: '', type: 'idcard', required: false, options: '' }
        ],
        project: [
          { label: '项目名称', name: '', type: 'input', required: true, options: '' },
          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },
          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },
          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },
          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },
          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },
          { label: '项目阶段', name: '', type: 'select', required: true, options: '创意阶段,初创阶段,成长阶段,成熟阶段' },
          { label: '预期投资金额', name: '', type: 'money', required: false, options: '' },
          { label: '项目网站', name: '', type: 'url', required: false, options: '' },
          { label: '项目评分', name: '', type: 'rate', required: false, options: '1,2,3,4,5' },
          { label: '是否同意条款', name: '', type: 'switch', required: true, options: '' },
          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },
          { label: '演示视频', name: '', type: 'file', required: false, options: '' }
        ],
        advanced: [
          { label: '用户姓名', name: '', type: 'input', required: true, options: '' },
          { label: '身份证号', name: '', type: 'idcard', required: true, options: '' },
          { label: '个人网站', name: '', type: 'url', required: false, options: '' },
          { label: '期望薪资', name: '', type: 'money', required: false, options: '' },
          { label: '技能等级', name: '', type: 'rate', required: false, options: '初级,中级,高级,专家,大师' },
          { label: '工作经验', name: '', type: 'slider', required: false, options: '' },
          { label: '是否接受调岗', name: '', type: 'switch', required: false, options: '' },
          { label: '喜欢的颜色', name: '', type: 'color', required: false, options: '' },
          { label: '入职时间', name: '', type: 'datetime', required: false, options: '' },
          { label: '技能标签', name: '', type: 'checkbox_other', required: false, options: 'Java,Python,JavaScript,Go,C++' },
          { label: '工作地点偏好', name: '', type: 'radio_other', required: false, options: '北京,上海,深圳,杭州,成都' },
          { label: '行业选择', name: '', type: 'select_other', required: false, options: '互联网,金融,教育,医疗,制造业' }
        ]
      };

      if (templates[command]) {
        this.currentFormFields = templates[command].map(field => ({
          ...field,
          name: this.generateUniqueFieldName(field.label.toLowerCase())
        }));
      }
    },
    /** 预览表单 */
    handlePreviewForm() {
      this.previewDialogVisible = true;
    },
    /** 保存表单配置 */
    saveFormConfig() {
      if (!this.currentConfig) {
        this.$modal.msgError("请先选择要配置的表单");
        return;
      }

      // 验证字段配置
      for (let i = 0; i < this.currentFormFields.length; i++) {
        const field = this.currentFormFields[i];
        if (!field.label) {
          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);
          return;
        }
        if (!field.name) {
          field.name = this.generateUniqueFieldName(field.label.toLowerCase());
        }
        if (this.needOptions(field.type) && !field.options) {
          this.$modal.msgError(`第${i + 1}个字段"${field.label}"需要设置选项内容`);
          return;
        }
      }

      const formData = {
        configId: this.currentConfig.configId,
        formConfig: JSON.stringify(this.currentFormFields)
      };

      updateFormConfig(formData).then(response => {
        this.$modal.msgSuccess("表单配置保存成功");
        this.formConfigOpen = false;
        this.loadEnabledConfig();
        this.getList();
      });
    },
    /** 获取字段选项 */
    getFieldOptions(options) {
      if (!options) return [];
      return options.split(',').map(opt => opt.trim()).filter(opt => opt);
    },
    /** 获取选项输入框占位符 */
    getOptionsPlaceholder(type) {
      const placeholders = {
        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',
        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',
        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',
        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加"其他"选项）',
        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加"其他"选项）',
        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加"其他"选项）',
        rate: '评分等级，用逗号分隔，如：1,2,3,4,5 或 差,一般,良好,优秀,卓越'
      };
      return placeholders[type] || '选项内容，多个选项用逗号分隔';
    },
    /** 获取字段图标 */
    getFieldIcon(type) {
      const icons = {
        input: 'el-icon-edit',
        textarea: 'el-icon-document',
        number: 'el-icon-s-data',
        email: 'el-icon-message',
        tel: 'el-icon-phone',
        radio: 'el-icon-circle-check',
        checkbox: 'el-icon-check',
        select: 'el-icon-arrow-down',
        radio_other: 'el-icon-circle-plus-outline',
        checkbox_other: 'el-icon-circle-plus-outline',
        select_other: 'el-icon-circle-plus-outline',
        date: 'el-icon-date',
        time: 'el-icon-time',
        datetime: 'el-icon-date',
        file: 'el-icon-upload',
        url: 'el-icon-link',
        idcard: 'el-icon-postcard',
        money: 'el-icon-coin',
        rate: 'el-icon-star-on',
        slider: 'el-icon-sort',
        switch: 'el-icon-switch-button',
        color: 'el-icon-brush'
      };
      return icons[type] || 'el-icon-edit';
    },
    /** 获取字段类型名称 */
    getFieldTypeName(type) {
      const names = {
        input: '文本输入',
        textarea: '多行文本',
        number: '数字输入',
        email: '邮箱',
        tel: '电话',
        radio: '单选',
        checkbox: '多选',
        select: '下拉选择',
        radio_other: '单选+其他',
        checkbox_other: '多选+其他',
        select_other: '下拉+其他',
        date: '日期',
        time: '时间',
        datetime: '日期时间',
        file: '文件上传',
        url: '网址链接',
        idcard: '身份证号',
        money: '金额',
        rate: '评分',
        slider: '滑块',
        switch: '开关',
        color: '颜色选择'
      };
      return names[type] || '未知类型';
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/haitang/formConfig/export', {
        ...this.queryParams
      }, `formConfig_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.form-config-container {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.config-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.config-content {
  padding: 24px;
  min-height: 200px;
}

.enabled-config {
  border: 1px solid #67c23a;
  border-radius: 6px;
  padding: 16px;
  background: #f0f9ff;
}

.enabled-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.enabled-header h4 {
  margin: 0;
  color: #67c23a;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.enabled-description {
  margin-bottom: 16px;
  color: #606266;
}

.form-preview h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.field-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.field-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.field-item:last-child {
  border-bottom: none;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.field-icon {
  color: #409eff;
  font-size: 16px;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-type {
  color: #909399;
  font-size: 12px;
}

.empty-form, .no-enabled-config {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-form i, .no-enabled-config i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.config-list-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.form-fields-config {
  max-height: 600px;
  overflow-y: auto;
}

.form-fields-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.form-fields-list {
  padding: 16px;
}

.empty-fields {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-fields i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.field-config-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.field-config-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
}

.field-index {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.field-config-body {
  padding: 12px 16px;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-field {
  margin-bottom: 20px;
}

.preview-label {
  display: block;
  margin-bottom: 8px;
  color: #303133;
  font-weight: 500;
}

.required {
  color: #f56c6c;
  margin-left: 4px;
}

.preview-input {
  width: 100%;
}
</style>

<style scoped>
.form-config-container {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.config-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.config-content {
  padding: 24px;
  min-height: 200px;
}

.enabled-config {
  border: 1px solid #67c23a;
  border-radius: 6px;
  padding: 16px;
  background: #f0f9ff;
}

.enabled-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.enabled-header h4 {
  margin: 0;
  color: #67c23a;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.enabled-description {
  margin-bottom: 16px;
  color: #606266;
}

.form-preview h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.field-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.field-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.field-item:last-child {
  border-bottom: none;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.field-icon {
  color: #409eff;
  font-size: 16px;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-type {
  color: #909399;
  font-size: 12px;
}

.empty-form, .no-enabled-config {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-form i, .no-enabled-config i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.config-list-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.form-fields-config {
  max-height: 600px;
  overflow-y: auto;
}

.form-fields-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.form-fields-list {
  padding: 16px;
}

.empty-fields {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-fields i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.field-config-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.field-config-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
}

.field-index {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.field-config-body {
  padding: 12px 16px;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-field {
  margin-bottom: 20px;
}

.preview-label {
  display: block;
  margin-bottom: 8px;
  color: #303133;
  font-weight: 500;
}

.required {
  color: #f56c6c;
  margin-left: 4px;
}

.preview-input {
  width: 100%;
}
</style>
