import request from '@/utils/request'

// 查询企业列表
export function listEnterprise(query) {
  return request({
    url: '/miniapp/enterprise/list',
    method: 'get',
    params: query
  })
}

// 查询企业详细
export function getEnterprise(enterpriseId) {
  return request({
    url: '/miniapp/enterprise/' + enterpriseId,
    method: 'get'
  })
}

// 新增企业
export function addEnterprise(data) {
  return request({
    url: '/miniapp/enterprise',
    method: 'post',
    data: data
  })
}

// 修改企业
export function updateEnterprise(data) {
  return request({
    url: '/miniapp/enterprise',
    method: 'put',
    data: data
  })
}

// 删除企业
export function delEnterprise(enterpriseId) {
  return request({
    url: '/miniapp/enterprise/' + enterpriseId,
    method: 'delete'
  })
}

// 查询企业产业关联
export function getEnterpriseIndustry(enterpriseId) {
  return request({
    url: '/miniapp/enterprise/industry/' + enterpriseId,
    method: 'get'
  })
}

// 更新企业产业关联
export function updateEnterpriseIndustry(data) {
  return request({
    url: '/miniapp/enterprise/industry',
    method: 'put',
    data: data
  })
}

// 更新企业产业关联（简化版本）
export function updateEnterpriseIndustrySimple(data) {
  return request({
    url: '/miniapp/enterprise/industry/update',
    method: 'post',
    data: data
  })
}

// 查询企业详细信息（包含产业信息）
export function getEnterpriseDetail(enterpriseId) {
  return request({
    url: '/miniapp/enterprise/detail/' + enterpriseId,
    method: 'get'
  })
}

// 查询产业类型列表
export function listIndustryType() {
  return request({
    url: '/miniapp/industry/type/list',
    method: 'post',
    data: { status: '0' }
  })
}

// 查询产业位置列表
export function listIndustryPosition(industryTypeId) {
  return request({
    url: '/miniapp/industry/position/getByIndustryType',
    method: 'post',
    data: industryTypeId || null
  })
}

// 查询产业细分列表
export function listIndustrySegment(positionId) {
  return request({
    url: '/miniapp/industry/segment/getByPosition',
    method: 'post',
    data: positionId || null
  })
}

// 导出企业
export function exportEnterprise(query) {
  return request({
    url: '/miniapp/enterprise/export',
    method: 'get',
    params: query
  })
} 