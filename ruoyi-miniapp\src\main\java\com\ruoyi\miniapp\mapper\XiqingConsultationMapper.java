package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.XiqingConsultation;
import org.apache.ibatis.annotations.Mapper;

/**
 * 西青金种子报名咨询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Mapper
public interface XiqingConsultationMapper 
{
    /**
     * 查询西青金种子报名咨询
     * 
     * @param consultationId 西青金种子报名咨询主键
     * @return 西青金种子报名咨询
     */
    public XiqingConsultation selectXiqingConsultationByConsultationId(Long consultationId);

    /**
     * 查询西青金种子报名咨询列表
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 西青金种子报名咨询集合
     */
    public List<XiqingConsultation> selectXiqingConsultationList(XiqingConsultation xiqingConsultation);

    /**
     * 新增西青金种子报名咨询
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 结果
     */
    public int insertXiqingConsultation(XiqingConsultation xiqingConsultation);

    /**
     * 修改西青金种子报名咨询
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 结果
     */
    public int updateXiqingConsultation(XiqingConsultation xiqingConsultation);

    /**
     * 删除西青金种子报名咨询
     * 
     * @param consultationId 西青金种子报名咨询主键
     * @return 结果
     */
    public int deleteXiqingConsultationByConsultationId(Long consultationId);

    /**
     * 批量删除西青金种子报名咨询
     * 
     * @param consultationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXiqingConsultationByConsultationIds(Long[] consultationIds);
}
