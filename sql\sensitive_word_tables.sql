-- 敏感词管理系统数据库表结构
-- 创建时间: 2025-01-17

-- 敏感词分类表
DROP TABLE IF EXISTS `sensitive_word_category`;
CREATE TABLE `sensitive_word_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序（数字越小越靠前）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_category_name` (`category_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='敏感词分类表';

-- 敏感词表
DROP TABLE IF EXISTS `sensitive_word`;
CREATE TABLE `sensitive_word` (
  `word_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `word_content` varchar(200) NOT NULL COMMENT '敏感词内容',
  `word_type` char(1) DEFAULT '1' COMMENT '词类型（1敏感词 2白名单）',
  `severity_level` char(1) DEFAULT '1' COMMENT '严重程度（1轻微 2中等 3严重）',
  `replacement_char` varchar(10) DEFAULT '*' COMMENT '替换字符',
  `is_regex` char(1) DEFAULT '0' COMMENT '是否正则表达式（0否 1是）',
  `hit_count` bigint(20) DEFAULT '0' COMMENT '命中次数',
  `last_hit_time` datetime DEFAULT NULL COMMENT '最后命中时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`word_id`),
  UNIQUE KEY `uk_word_content` (`word_content`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_word_type` (`word_type`),
  KEY `idx_severity_level` (`severity_level`),
  KEY `idx_status` (`status`),
  KEY `idx_hit_count` (`hit_count`),
  CONSTRAINT `fk_sensitive_word_category` FOREIGN KEY (`category_id`) REFERENCES `sensitive_word_category` (`category_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- 敏感词检测日志表
DROP TABLE IF EXISTS `sensitive_word_log`;
CREATE TABLE `sensitive_word_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名',
  `module_name` varchar(50) DEFAULT NULL COMMENT '模块名称',
  `operation_type` varchar(20) DEFAULT NULL COMMENT '操作类型（检测/过滤/替换）',
  `original_content` text COMMENT '原始内容',
  `filtered_content` text COMMENT '过滤后内容',
  `hit_words` text COMMENT '命中的敏感词（JSON格式）',
  `hit_count` int(11) DEFAULT '0' COMMENT '命中数量',
  `client_ip` varchar(128) DEFAULT '' COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT '' COMMENT '用户代理',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module_name` (`module_name`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='敏感词检测日志表';

-- 插入默认分类数据
INSERT INTO `sensitive_word_category` (`category_name`, `category_code`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('政治敏感', 'POLITICAL', '涉及政治相关的敏感词汇', 1, '0', 'admin', NOW()),
('色情低俗', 'PORNOGRAPHIC', '涉及色情、低俗内容的敏感词汇', 2, '0', 'admin', NOW()),
('暴力血腥', 'VIOLENT', '涉及暴力、血腥内容的敏感词汇', 3, '0', 'admin', NOW()),
('违法犯罪', 'ILLEGAL', '涉及违法犯罪活动的敏感词汇', 4, '0', 'admin', NOW()),
('赌博诈骗', 'GAMBLING', '涉及赌博、诈骗等非法活动的敏感词汇', 5, '0', 'admin', NOW()),
('广告垃圾', 'SPAM', '垃圾广告、恶意推广等敏感词汇', 6, '0', 'admin', NOW()),
('其他敏感', 'OTHER', '其他类型的敏感词汇', 99, '0', 'admin', NOW());

-- 插入一些示例敏感词数据
INSERT INTO `sensitive_word` (`category_id`, `word_content`, `word_type`, `severity_level`, `status`, `create_by`, `create_time`) VALUES
(2, '色情', '1', '3', '0', 'admin', NOW()),
(2, '黄色', '1', '2', '0', 'admin', NOW()),
(3, '暴力', '1', '3', '0', 'admin', NOW()),
(4, '毒品', '1', '3', '0', 'admin', NOW()),
(5, '赌博', '1', '3', '0', 'admin', NOW()),
(6, '垃圾广告', '1', '2', '0', 'admin', NOW()),
(7, '测试敏感词', '1', '1', '0', 'admin', NOW());

-- 插入一些白名单词汇
INSERT INTO `sensitive_word` (`category_id`, `word_content`, `word_type`, `severity_level`, `status`, `create_by`, `create_time`) VALUES
(7, '正常词汇', '2', '1', '0', 'admin', NOW()),
(7, '合法内容', '2', '1', '0', 'admin', NOW());
