# 富文本编辑器URL修复说明

## 问题描述
原来的富文本编辑器在插入图片时使用的是相对路径拼接：
```javascript
// 原来的逻辑（有问题）
quill.insertEmbed(length, "image", process.env.VUE_APP_BASE_API + res.fileName)
// 结果：/dev-api/profile/upload/2025/07/18/image.jpg
```

这种方式在小程序端无法正常显示图片，因为小程序无法访问 `/dev-api` 这样的相对路径。

## 解决方案
修改富文本编辑器逻辑，使用后端返回的完整URL：
```javascript
// 修改后的逻辑（正确）
quill.insertEmbed(length, "image", res.url)
// 结果：http://************:8080/profile/upload/2025/07/18/image.jpg
```

## 修改内容

### 1. 图片上传成功处理
**文件**: `ruoyi-ui/src/components/Editor/index.vue`
**方法**: `handleUploadSuccess`
```javascript
// 修改前
quill.insertEmbed(length, "image", process.env.VUE_APP_BASE_API + res.fileName)

// 修改后
quill.insertEmbed(length, "image", res.url)
```

### 2. 视频上传成功处理
**文件**: `ruoyi-ui/src/components/Editor/index.vue`
**方法**: `handleVideoUploadSuccess`
```javascript
// 修改前
quill.insertEmbed(length, "video", process.env.VUE_APP_BASE_API + res.fileName)

// 修改后
quill.insertEmbed(length, "video", res.url)
```

## 后端URL生成逻辑

### 1. 上传接口返回数据
**文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/common/CommonController.java`
```java
String fileName = FileUploadUtils.upload(filePath, file);
String url = serverConfig.getUrl() + fileName;  // 完整URL
AjaxResult ajax = AjaxResult.success();
ajax.put("url", url);           // 完整URL，供小程序使用
ajax.put("fileName", fileName); // 相对路径，供其他场景使用
```

### 2. ServerConfig URL生成
**文件**: `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ServerConfig.java`
```java
public String getUrl() {
    HttpServletRequest request = ServletUtils.getRequest();
    return getDomain(request);  // 返回：http://************:8080
}
```

## URL格式对比

### 修改前（有问题）
- **管理端存储**: `/dev-api/profile/upload/2025/07/18/image.jpg`
- **小程序访问**: 无法访问（路径不完整）

### 修改后（正确）
- **管理端存储**: `http://************:8080/profile/upload/2025/07/18/image.jpg`
- **小程序访问**: 可以正常访问（完整URL）

## 兼容性说明

### 1. 管理端访问
- **开发环境**: 通过代理正常访问
- **生产环境**: 直接访问后端服务器

### 2. 小程序端访问
- **开发环境**: 直接访问 `http://************:8080`
- **生产环境**: 访问实际的生产服务器地址

### 3. 历史数据兼容
- 已存在的相对路径数据需要在展示时进行URL补全
- 新数据将使用完整URL，无需额外处理

## 注意事项

### 1. 跨域配置
小程序端需要在后端配置CORS，允许跨域访问：
```java
// 在SecurityConfig或其他配置中添加
@CrossOrigin(origins = "*")
```

### 2. 域名配置
生产环境需要确保ServerConfig返回正确的域名：
- 开发环境：`http://************:8080`
- 生产环境：`https://your-domain.com`

### 3. HTTPS支持
生产环境建议使用HTTPS，确保图片访问安全。

## 测试验证

### 1. 管理端测试
1. 在富文本编辑器中上传图片
2. 检查生成的HTML中图片URL是否为完整路径
3. 保存后重新编辑，确认图片正常显示

### 2. 小程序端测试
1. 获取包含图片的富文本内容
2. 在小程序中渲染HTML
3. 确认图片能够正常加载显示

### 3. 数据库检查
查看数据库中存储的富文本内容，确认图片URL格式：
```sql
SELECT content FROM mini_notice WHERE content LIKE '%<img%';
```

应该看到类似这样的内容：
```html
<p><img src="http://************:8080/profile/upload/2025/07/18/image.jpg"></p>
```

## 总结
这个修改确保了富文本中的图片和视频在所有环境下都能正常访问，特别是解决了小程序端无法显示图片的问题。
