import request from '@/utils/request'

// 查询天大海棠杯项目报名记录列表
export function listProjectRegistration(query) {
  return request({
    url: '/miniapp/haitang/registration/list',
    method: 'get',
    params: query
  })
}

// 查询天大海棠杯项目报名记录详细
export function getProjectRegistration(registrationId) {
  return request({
    url: '/miniapp/haitang/registration/' + registrationId,
    method: 'get'
  })
}

// 新增天大海棠杯项目报名记录
export function addProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/registration',
    method: 'post',
    data: data
  })
}

// 修改天大海棠杯项目报名记录
export function updateProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/registration',
    method: 'put',
    data: data
  })
}

// 删除天大海棠杯项目报名记录
export function delProjectRegistration(registrationId) {
  return request({
    url: '/miniapp/haitang/registration/' + registrationId,
    method: 'delete'
  })
}



// 导出天大海棠杯项目报名记录
export function exportProjectRegistration(query) {
  return request({
    url: '/miniapp/haitang/registration/export',
    method: 'post',
    params: query
  })
}
