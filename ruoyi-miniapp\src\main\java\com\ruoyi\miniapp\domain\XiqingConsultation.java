package com.ruoyi.miniapp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 西青金种子报名咨询对象 xiqing_consultation
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public class XiqingConsultation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 咨询ID */
    private Long consultationId;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactMethod;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setConsultationId(Long consultationId) 
    {
        this.consultationId = consultationId;
    }

    public Long getConsultationId() 
    {
        return consultationId;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactMethod(String contactMethod) 
    {
        this.contactMethod = contactMethod;
    }

    public String getContactMethod() 
    {
        return contactMethod;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("consultationId", getConsultationId())
            .append("contactName", getContactName())
            .append("contactMethod", getContactMethod())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
