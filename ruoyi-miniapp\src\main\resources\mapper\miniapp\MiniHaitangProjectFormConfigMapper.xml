<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniHaitangProjectFormConfigMapper">
    
    <resultMap type="MiniHaitangProjectFormConfig" id="MiniHaitangProjectFormConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="configName"    column="config_name"    />
        <result property="configDescription"    column="config_description"    />
        <result property="formConfig"    column="form_config"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniHaitangProjectFormConfigVo">
        select config_id, config_name, config_description, form_config, is_enabled, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_haitang_project_form_config
    </sql>

    <select id="selectMiniHaitangProjectFormConfigList" parameterType="MiniHaitangProjectFormConfig" resultMap="MiniHaitangProjectFormConfigResult">
        <include refid="selectMiniHaitangProjectFormConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniHaitangProjectFormConfigByConfigId" parameterType="Long" resultMap="MiniHaitangProjectFormConfigResult">
        <include refid="selectMiniHaitangProjectFormConfigVo"/>
        where config_id = #{configId}
    </select>

    <select id="selectEnabledFormConfig" resultMap="MiniHaitangProjectFormConfigResult">
        <include refid="selectMiniHaitangProjectFormConfigVo"/>
        where is_enabled = '1' and status = '0'
        order by sort_order asc
        limit 1
    </select>

    <select id="selectEnabledMiniHaitangProjectFormConfigList" resultMap="MiniHaitangProjectFormConfigResult">
        <include refid="selectMiniHaitangProjectFormConfigVo"/>
        where is_enabled = '1' and status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniHaitangProjectFormConfig" parameterType="MiniHaitangProjectFormConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into mini_haitang_project_form_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="configDescription != null">config_description,</if>
            <if test="formConfig != null">form_config,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="configDescription != null">#{configDescription},</if>
            <if test="formConfig != null">#{formConfig},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniHaitangProjectFormConfig" parameterType="MiniHaitangProjectFormConfig">
        update mini_haitang_project_form_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="configDescription != null">config_description = #{configDescription},</if>
            <if test="formConfig != null">form_config = #{formConfig},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteMiniHaitangProjectFormConfigByConfigId" parameterType="Long">
        delete from mini_haitang_project_form_config where config_id = #{configId}
    </delete>

    <delete id="deleteMiniHaitangProjectFormConfigByConfigIds" parameterType="String">
        delete from mini_haitang_project_form_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

    <update id="disableAllFormConfigs">
        update mini_haitang_project_form_config set is_enabled = '0'
    </update>

    <update id="enableFormConfig" parameterType="Long">
        update mini_haitang_project_form_config set is_enabled = '1' where config_id = #{configId}
    </update>

</mapper>
