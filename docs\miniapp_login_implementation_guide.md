# 小程序登录实现指南

## 概述

本指南提供了完整的小程序登录解决方案，包括后端接口和前端实现。该方案支持智能登录，能够自动处理新用户注册和老用户登录。

## 后端接口

### 1. 检查用户状态接口

**接口地址**: `POST /miniapp/user/checkUserStatus`

**功能**: 检查用户是否已注册，是否需要手机号授权

**请求参数**:
```json
{
  "code": "微信登录凭证"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "检查成功",
  "data": {
    "isRegistered": true,
    "needPhoneAuth": false,
    "openid": "用户openid",
    "userInfo": {
      "userId": 123,
      "nickName": "用户昵称",
      "avatar": "头像URL",
      "hasPhone": true,
      "hasRealName": false,
      "totalPoints": 100
    }
  }
}
```

### 2. 登录注册接口

**接口地址**: `POST /miniapp/user/weixinLogin`

**功能**: 统一的登录注册接口，自动处理新用户注册和老用户登录

**请求参数**:
```json
{
  "code": "微信登录凭证（必填）",
  "encryptedData": "加密数据（手机号授权时必填）",
  "iv": "初始向量（手机号授权时必填）",
  "nickName": "用户昵称（可选）",
  "avatar": "头像URL（可选）"
}
```

## 前端实现

### 1. 文件结构

```
├── utils/
│   └── login-utils.js          # 登录工具类
├── pages/
│   └── login/
│       ├── login.js           # 登录页面逻辑
│       ├── login.wxml         # 登录页面结构
│       └── login.wxss         # 登录页面样式
```

### 2. 快速集成

#### 步骤1: 复制工具类
将 `docs/miniapp_login_utils_simple.js` 复制到项目的 `utils/login-utils.js`

#### 步骤2: 修改API地址
在 `login-utils.js` 中修改 `API_BASE_URL` 为实际的API地址

#### 步骤3: 在页面中使用

```javascript
const LoginUtils = require('../../utils/login-utils.js');

Page({
  data: {
    loading: false
  },

  onLoad() {
    // 检查是否已登录
    if (LoginUtils.isLoggedIn()) {
      this.navigateToMain();
      return;
    }
    
    // 执行智能登录
    this.smartLogin();
  },

  // 智能登录
  smartLogin() {
    this.setData({ loading: true });
    
    LoginUtils.smartLogin()
      .then((result) => {
        if (result.needUserChoice) {
          // 需要用户选择登录方式
          this.showLoginOptions(result);
        } else {
          // 登录成功
          this.handleLoginSuccess(result);
        }
      })
      .catch((error) => {
        this.showError(error.message);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 快速登录按钮
  onQuickLogin() {
    this.setData({ loading: true });
    
    LoginUtils.quickLogin()
      .then((result) => {
        this.handleLoginSuccess(result);
      })
      .catch((error) => {
        this.showError(error.message);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 手机号授权按钮
  onGetPhoneNumber(e) {
    this.setData({ loading: true });
    
    LoginUtils.phoneLogin(e.detail)
      .then((result) => {
        this.handleLoginSuccess(result);
      })
      .catch((error) => {
        this.showError(error.message);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 处理登录成功
  handleLoginSuccess(result) {
    const message = result.isNewUser ? '欢迎新用户！' : '欢迎回来！';
    wx.showToast({
      title: message,
      icon: 'success'
    });
    
    setTimeout(() => {
      this.navigateToMain();
    }, 1500);
  },

  // 跳转到主页
  navigateToMain() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 显示错误
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });
  }
});
```

### 3. 页面结构示例

```xml
<view class="login-container">
  <view wx:if="{{loading}}" class="loading">
    <text>正在登录...</text>
  </view>
  
  <view wx:else class="login-content">
    <view class="header">
      <image class="logo" src="/images/logo.png"></image>
      <text class="title">欢迎使用小程序</text>
    </view>
    
    <view class="buttons">
      <button class="btn primary" bindtap="onQuickLogin">
        快速登录
      </button>
      
      <button 
        class="btn secondary" 
        open-type="getPhoneNumber" 
        bindgetphonenumber="onGetPhoneNumber">
        手机号登录
      </button>
    </view>
  </view>
</view>
```

## 使用场景

### 1. 应用启动时自动登录

```javascript
// app.js
App({
  onLaunch() {
    const LoginUtils = require('./utils/login-utils.js');
    
    if (!LoginUtils.isLoggedIn()) {
      // 未登录，跳转到登录页
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }
  }
});
```

### 2. 需要登录的页面

```javascript
// 其他页面
Page({
  onLoad() {
    const LoginUtils = require('../../utils/login-utils.js');
    
    if (!LoginUtils.isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 继续页面逻辑
    this.loadData();
  },

  // 发起需要认证的请求
  loadData() {
    const LoginUtils = require('../../utils/login-utils.js');
    
    LoginUtils.authRequest({
      url: '/api/user/data',
      method: 'GET'
    }).then((res) => {
      // 处理数据
    }).catch((error) => {
      if (error.message.includes('登录已过期')) {
        // 跳转到登录页
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }
    });
  }
});
```

## 注意事项

1. **API地址配置**: 记得在 `login-utils.js` 中修改 `API_BASE_URL`
2. **手机号授权**: 必须使用 `<button open-type="getPhoneNumber">` 组件
3. **错误处理**: 建议添加网络错误和登录失败的处理逻辑
4. **用户体验**: 可以添加加载动画和友好的提示信息
5. **安全性**: token应该设置合理的过期时间，并在过期时自动清理

## 优势

1. **智能判断**: 自动识别新老用户，选择合适的登录流程
2. **用户友好**: 不强制要求手机号授权，提供快速登录选项
3. **统一接口**: 后端使用同一个接口处理登录和注册
4. **易于集成**: 提供完整的工具类和示例代码
5. **错误处理**: 完善的错误处理和用户提示机制
