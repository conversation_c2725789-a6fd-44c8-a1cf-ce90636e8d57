package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniUserFollow;

/**
 * 用户关注Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IMiniUserFollowService 
{
    /**
     * 查询用户关注
     * 
     * @param followId 用户关注主键
     * @return 用户关注
     */
    public MiniUserFollow selectMiniUserFollowByFollowId(Long followId);

    /**
     * 查询用户关注列表
     * 
     * @param miniUserFollow 用户关注
     * @return 用户关注集合
     */
    public List<MiniUserFollow> selectMiniUserFollowList(MiniUserFollow miniUserFollow);

    /**
     * 新增用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    public int insertMiniUserFollow(MiniUserFollow miniUserFollow);

    /**
     * 修改用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    public int updateMiniUserFollow(MiniUserFollow miniUserFollow);

    /**
     * 批量删除用户关注
     * 
     * @param followIds 需要删除的用户关注主键集合
     * @return 结果
     */
    public int deleteMiniUserFollowByFollowIds(Long[] followIds);

    /**
     * 删除用户关注信息
     * 
     * @param followId 用户关注主键
     * @return 结果
     */
    public int deleteMiniUserFollowByFollowId(Long followId);

    /**
     * 关注用户
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 结果
     */
    public boolean followUser(Long followerId, Long followedId);

    /**
     * 取消关注用户
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 结果
     */
    public boolean unfollowUser(Long followerId, Long followedId);

    /**
     * 检查用户是否已关注某人
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 是否已关注
     */
    public boolean isFollowing(Long followerId, Long followedId);

    /**
     * 获取用户的关注列表（我关注的人）
     * 
     * @param followerId 关注者ID
     * @return 关注列表
     */
    public List<MiniUserFollow> getMyFollowingList(Long followerId);

    /**
     * 获取用户的粉丝列表（关注我的人）
     * 
     * @param followedId 被关注者ID
     * @return 粉丝列表
     */
    public List<MiniUserFollow> getMyFollowersList(Long followedId);

    /**
     * 获取用户关注数量
     * 
     * @param userId 用户ID
     * @return 关注数量
     */
    public int getFollowingCount(Long userId);

    /**
     * 获取用户粉丝数量
     * 
     * @param userId 用户ID
     * @return 粉丝数量
     */
    public int getFollowersCount(Long userId);

    /**
     * 检查是否互相关注
     * 
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 是否互相关注
     */
    public boolean isMutualFollow(Long userId1, Long userId2);

    /**
     * 批量查询用户关注状态
     * 
     * @param followerId 关注者ID
     * @param followedIds 被关注者ID列表
     * @return 关注状态列表
     */
    public List<MiniUserFollow> batchGetFollowStatus(Long followerId, List<Long> followedIds);

    /**
     * 更新用户关注统计数据
     * 
     * @param userId 用户ID
     */
    public void updateUserFollowCounts(Long userId);
}
