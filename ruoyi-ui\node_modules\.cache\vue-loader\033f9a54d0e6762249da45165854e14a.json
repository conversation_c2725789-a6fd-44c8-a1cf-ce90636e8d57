{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=style&index=1&id=7928017a&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754299061748}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753843472439}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753843491236}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753843480355}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS1jb25maWctY29udGFpbmVyIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmNvbmZpZy1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMjBweCAyNHB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7Cn0KCi5jb25maWctaGVhZGVyIGgzIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxOHB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5jb25maWctY29udGVudCB7CiAgcGFkZGluZzogMjRweDsKICBtaW4taGVpZ2h0OiAyMDBweDsKfQoKLmVuYWJsZWQtY29uZmlnIHsKICBib3JkZXI6IDFweCBzb2xpZCAjNjdjMjNhOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBwYWRkaW5nOiAxNnB4OwogIGJhY2tncm91bmQ6ICNmMGY5ZmY7Cn0KCi5lbmFibGVkLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxMnB4Owp9CgouZW5hYmxlZC1oZWFkZXIgaDQgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzY3YzIzYTsKICBmb250LXNpemU6IDE2cHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogOHB4Owp9CgouZW5hYmxlZC1kZXNjcmlwdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKICBjb2xvcjogIzYwNjI2NjsKfQoKLmZvcm0tcHJldmlldyBoNSB7CiAgbWFyZ2luOiAwIDAgMTJweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLmZpZWxkLWxpc3QgewogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5maWVsZC1pdGVtIHsKICBwYWRkaW5nOiAxMnB4IDE2cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgYmFja2dyb3VuZDogI2ZhZmJmYzsKfQoKLmZpZWxkLWl0ZW06bGFzdC1jaGlsZCB7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKfQoKLmZpZWxkLWluZm8gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDEycHg7Cn0KCi5maWVsZC1pY29uIHsKICBjb2xvcjogIzQwOWVmZjsKICBmb250LXNpemU6IDE2cHg7Cn0KCi5maWVsZC1sYWJlbCB7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzMwMzEzMzsKfQoKLmZpZWxkLXR5cGUgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLmVtcHR5LWZvcm0sIC5uby1lbmFibGVkLWNvbmZpZyB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDQwcHggMjBweDsKICBjb2xvcjogIzkwOTM5OTsKfQoKLmVtcHR5LWZvcm0gaSwgLm5vLWVuYWJsZWQtY29uZmlnIGkgewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGRpc3BsYXk6IGJsb2NrOwp9CgouY29uZmlnLWxpc3QtY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5saXN0LWhlYWRlciB7CiAgcGFkZGluZzogMjBweCAyNHB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmxpc3QtaGVhZGVyIGg0IHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5mb3JtLWZpZWxkcy1jb25maWcgewogIG1heC1oZWlnaHQ6IDYwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5mb3JtLWZpZWxkcy10b29sYmFyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE2cHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLnRvb2xiYXItbGVmdCB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEycHg7Cn0KCi5mb3JtLWZpZWxkcy1saXN0IHsKICBwYWRkaW5nOiAxNnB4Owp9CgouZW1wdHktZmllbGRzIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNDBweCAyMHB4OwogIGNvbG9yOiAjOTA5Mzk5Owp9CgouZW1wdHktZmllbGRzIGkgewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGRpc3BsYXk6IGJsb2NrOwp9CgouZmllbGQtY29uZmlnLWl0ZW0gewogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIG1hcmdpbi1ib3R0b206IDEycHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmZpZWxkLWNvbmZpZy1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDEycHg7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIGJhY2tncm91bmQ6ICNmYWZiZmM7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7Cn0KCi5maWVsZC1pbmRleCB7CiAgZGlzcGxheTogaW5saW5lLWZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICB3aWR0aDogMjRweDsKICBoZWlnaHQ6IDI0cHg7CiAgYmFja2dyb3VuZDogIzQwOWVmZjsKICBjb2xvcjogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogNTAlOwogIGZvbnQtc2l6ZTogMTJweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLmZpZWxkLWNvbmZpZy1ib2R5IHsKICBwYWRkaW5nOiAxMnB4IDE2cHg7Cn0KCi5wcmV2aWV3LWhlYWRlciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDI0cHg7CiAgcGFkZGluZy1ib3R0b206IDE2cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7Cn0KCi5wcmV2aWV3LWhlYWRlciBoMyB7CiAgbWFyZ2luOiAwIDAgOHB4IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxOHB4Owp9CgoucHJldmlldy1oZWFkZXIgcCB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjNjA2MjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnByZXZpZXctZmllbGQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5wcmV2aWV3LWxhYmVsIHsKICBkaXNwbGF5OiBibG9jazsKICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLnJlcXVpcmVkIHsKICBjb2xvcjogI2Y1NmM2YzsKICBtYXJnaW4tbGVmdDogNHB4Owp9CgoucHJldmlldy1pbnB1dCB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+kCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectFormConfig", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 表单配置管理区域 -->\n    <div class=\"form-config-container\">\n      <div class=\"config-header\">\n        <h3>项目报名表单配置管理</h3>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd\"\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\n          >新增配置</el-button>\n        </div>\n      </div>\n\n      <div class=\"config-content\" v-loading=\"loading\">\n        <!-- 当前启用的配置 -->\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\n          <div class=\"enabled-header\">\n            <h4>\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\n              当前启用配置：{{ enabledConfig.configName }}\n            </h4>\n            <div class=\"enabled-actions\">\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                icon=\"el-icon-setting\"\n                @click=\"handleFormConfig(enabledConfig)\"\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              >配置表单</el-button>\n              <el-button\n                size=\"small\"\n                type=\"success\"\n                icon=\"el-icon-view\"\n                @click=\"handlePreview(enabledConfig)\"\n              >预览表单</el-button>\n            </div>\n          </div>\n          <div class=\"enabled-description\">\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\n          </div>\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\n            <h5>表单字段预览：</h5>\n            <div class=\"field-list\">\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\n                <div class=\"field-info\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                  <span class=\"field-label\">{{ field.label }}</span>\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div v-else class=\"empty-form\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\n          </div>\n        </div>\n\n        <!-- 无启用配置时的提示 -->\n        <div v-else class=\"no-enabled-config\">\n          <i class=\"el-icon-warning-outline\"></i>\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 所有配置列表 -->\n    <div class=\"config-list-container\">\n      <div class=\"list-header\">\n        <h4>所有表单配置</h4>\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\n          <el-form-item label=\"配置名称\" prop=\"configName\">\n            <el-input\n              v-model=\"queryParams.configName\"\n              placeholder=\"请输入配置名称\"\n              clearable\n              style=\"width: 200px;\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\n              <el-option\n                v-for=\"dict in dict.type.sys_normal_disable\"\n                :key=\"dict.value\"\n                :label=\"dict.label\"\n                :value=\"dict.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\n            <el-tag v-else type=\"info\">未启用</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >配置</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit\"\n              @click=\"handleUpdate(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >编辑</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-check\"\n              @click=\"handleEnable(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              v-if=\"scope.row.isEnabled !== '1'\"\n            >启用</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-delete\"\n              @click=\"handleDelete(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </div>\n\n    <!-- 添加或修改配置基本信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"配置名称\" prop=\"configName\">\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\n        </el-form-item>\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单配置对话框 -->\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\n      <div class=\"form-fields-config\">\n        <!-- 工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\n              <el-button size=\"small\">\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"advanced\">高级字段模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\n          </div>\n        </div>\n\n        <!-- 字段配置列表 -->\n        <div class=\"form-fields-list\">\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\n          </div>\n          <div v-else>\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\n              <div class=\"field-config-header\">\n                <span class=\"field-index\">{{ index + 1 }}</span>\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\">\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\n                  <el-option label=\"📞 电话\" value=\"tel\" />\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                  <el-option label=\"📅 日期\" value=\"date\" />\n                  <el-option label=\"⏰ 时间\" value=\"time\" />\n                  <el-option label=\"📅⏰ 日期时间\" value=\"datetime\" />\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\n                  <el-option label=\"🔗 网址链接\" value=\"url\" />\n                  <el-option label=\"🏢 身份证号\" value=\"idcard\" />\n                  <el-option label=\"💰 金额\" value=\"money\" />\n                  <el-option label=\"📏 评分\" value=\"rate\" />\n                  <el-option label=\"🎚️ 滑块\" value=\"slider\" />\n                  <el-option label=\"🔄 开关\" value=\"switch\" />\n                  <el-option label=\"🌈 颜色选择\" value=\"color\" />\n                </el-select>\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\n              </div>\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\n                <el-input\n                  v-model=\"field.options\"\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\n                  size=\"small\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <!-- 基础输入类型 -->\n              <el-input\n                v-if=\"['input', 'email', 'tel', 'url', 'idcard'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 数字类型 -->\n              <el-input-number\n                v-else-if=\"['number', 'money'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 单选类型 -->\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-radio>\n              </el-radio-group>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\n                  <el-radio\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-radio>\n                  <el-radio label=\"其他\">其他</el-radio>\n                </el-radio-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 多选类型 -->\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-checkbox>\n              </el-checkbox-group>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\n                  <el-checkbox\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-checkbox>\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\n                </el-checkbox-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 下拉选择 -->\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\n                <el-option\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                  :value=\"option\"\n                />\n              </el-select>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\n                  <el-option\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                    :value=\"option\"\n                  />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 日期时间类型 -->\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-time-picker\n                v-else-if=\"field.type === 'time'\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-date-picker\n                v-else-if=\"field.type === 'datetime'\"\n                type=\"datetime\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 评分 -->\n              <el-rate\n                v-else-if=\"field.type === 'rate'\"\n                disabled\n                :max=\"getFieldOptions(field.options).length || 5\"\n              />\n              <!-- 滑块 -->\n              <el-slider\n                v-else-if=\"field.type === 'slider'\"\n                disabled\n                :max=\"100\"\n                style=\"margin: 12px 0;\"\n              />\n              <!-- 开关 -->\n              <el-switch\n                v-else-if=\"field.type === 'switch'\"\n                disabled\n              />\n              <!-- 颜色选择 -->\n              <el-color-picker\n                v-else-if=\"field.type === 'color'\"\n                disabled\n              />\n              <!-- 文件上传 -->\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :auto-upload=\"false\"\n                disabled\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\n              </el-upload>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig } from \"@/api/miniapp/haitang/formConfig\";\nimport request from '@/utils/request';\n\nexport default {\n  name: \"FormConfig\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      listLoading: false,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 天大海棠杯项目报名表单配置表格数据\n      formConfigList: [],\n      // 当前启用的配置\n      enabledConfig: null,\n      // 启用配置的表单字段\n      enabledFormFields: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 当前配置的表单字段\n      currentFormFields: [],\n      // 当前操作的配置\n      currentConfig: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        configName: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        configName: [\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadEnabledConfig();\n    this.getList();\n  },\n  methods: {\n    /** 加载启用的配置 */\n    loadEnabledConfig() {\n      this.loading = true;\n      request({\n        url: '/miniapp/haitang/formConfig/enabled',\n        method: 'get'\n      }).then(response => {\n        if (response.data) {\n          this.enabledConfig = response.data;\n          this.loadEnabledFormFields();\n        } else {\n          this.enabledConfig = null;\n          this.enabledFormFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.enabledConfig = null;\n        this.enabledFormFields = [];\n        this.loading = false;\n      });\n    },\n    /** 加载启用配置的表单字段 */\n    loadEnabledFormFields() {\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\n        try {\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\n        } catch (e) {\n          this.enabledFormFields = [];\n        }\n      } else {\n        this.enabledFormFields = [];\n      }\n    },\n    /** 查询天大海棠杯项目报名表单配置列表 */\n    getList() {\n      this.listLoading = true;\n      listFormConfig(this.queryParams).then(response => {\n        this.formConfigList = response.rows;\n        this.total = response.total;\n        this.listLoading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        configId: null,\n        configName: null,\n        configDescription: null,\n        formConfig: null,\n        isEnabled: \"0\",\n        sortOrder: 0,\n        status: \"0\",\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.configId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加天大海棠杯项目报名表单配置\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const configId = row.configId || this.ids\n      getFormConfig(configId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改天大海棠杯项目报名表单配置\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.configId != null) {\n            updateFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const configIds = row.configId || this.ids;\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\n        return delFormConfig(configIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.formConfigOpen = true;\n    },\n    /** 预览按钮操作 */\n    handlePreview(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 启用按钮操作 */\n    handleEnable(row) {\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\n        return enableFormConfig(row.configId);\n      }).then(() => {\n        this.loadEnabledConfig();\n        this.getList();\n        this.$modal.msgSuccess(\"启用成功\");\n      }).catch(() => {});\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      const defaultName = this.generateUniqueFieldName('field');\n      this.currentFormFields.push({\n        name: defaultName,\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.currentFormFields.splice(index, 1);\n    },\n    /** 生成唯一字段名 */\n    generateUniqueFieldName(prefix) {\n      let counter = 1;\n      let name = prefix + counter;\n      while (this.currentFormFields.some(field => field.name === name)) {\n        counter++;\n        name = prefix + counter;\n      }\n      return name;\n    },\n    /** 判断字段类型是否需要选项 */\n    needOptions(type) {\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'rate'].includes(type);\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$modal.confirm('确认清空所有字段？').then(() => {\n          this.currentFormFields = [];\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' },\n          { label: '身份证号', name: '', type: 'idcard', required: false, options: '' }\n        ],\n        project: [\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '项目阶段', name: '', type: 'select', required: true, options: '创意阶段,初创阶段,成长阶段,成熟阶段' },\n          { label: '预期投资金额', name: '', type: 'money', required: false, options: '' },\n          { label: '项目网站', name: '', type: 'url', required: false, options: '' },\n          { label: '项目评分', name: '', type: 'rate', required: false, options: '1,2,3,4,5' },\n          { label: '是否同意条款', name: '', type: 'switch', required: true, options: '' },\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.currentFormFields = templates[command].map(field => ({\n          ...field,\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\n        }));\n      }\n    },\n    /** 预览表单 */\n    handlePreviewForm() {\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (!this.currentConfig) {\n        this.$modal.msgError(\"请先选择要配置的表单\");\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.currentFormFields.length; i++) {\n        const field = this.currentFormFields[i];\n        if (!field.label) {\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (!field.name) {\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\n        }\n        if (this.needOptions(field.type) && !field.options) {\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\n          return;\n        }\n      }\n\n      const formData = {\n        configId: this.currentConfig.configId,\n        formConfig: JSON.stringify(this.currentFormFields)\n      };\n\n      updateFormConfig(formData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadEnabledConfig();\n        this.getList();\n      });\n    },\n    /** 获取字段选项 */\n    getFieldOptions(options) {\n      if (!options) return [];\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\n    },\n    /** 获取选项输入框占位符 */\n    getOptionsPlaceholder(type) {\n      const placeholders = {\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        rate: '评分等级，用逗号分隔，如：1,2,3,4,5 或 差,一般,良好,优秀,卓越'\n      };\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-circle-check',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus-outline',\n        checkbox_other: 'el-icon-circle-plus-outline',\n        select_other: 'el-icon-circle-plus-outline',\n        date: 'el-icon-date',\n        time: 'el-icon-time',\n        datetime: 'el-icon-date',\n        file: 'el-icon-upload',\n        url: 'el-icon-link',\n        idcard: 'el-icon-postcard',\n        money: 'el-icon-coin',\n        rate: 'el-icon-star-on',\n        slider: 'el-icon-sort',\n        switch: 'el-icon-switch-button',\n        color: 'el-icon-brush'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const names = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        time: '时间',\n        datetime: '日期时间',\n        file: '文件上传',\n        url: '网址链接',\n        idcard: '身份证号',\n        money: '金额',\n        rate: '评分',\n        slider: '滑块',\n        switch: '开关',\n        color: '颜色选择'\n      };\n      return names[type] || '未知类型';\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/haitang/formConfig/export', {\n        ...this.queryParams\n      }, `formConfig_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"]}]}