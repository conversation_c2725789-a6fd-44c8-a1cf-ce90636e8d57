# 生产环境部署修复说明

## 问题描述
生产环境出现SQL错误：
```
Unknown column 'status' in 'field list'
SQL: select id, project_name, ..., status, audit_time, audit_remark, ... from mini_project_registration
```

## 根本原因
生产环境的jar包(`/www/wwwroot/tjuhaitang_minapp/ruoyi-admin.jar`)还是旧版本，包含了未修改的ProjectRegistrationMapper.xml文件，该文件仍然查询已删除的数据库字段。

## 解决方案

### 1. 重新构建项目
```bash
# 清理并重新编译
mvn clean package -DskipTests

# 或者使用IDE的Build -> Rebuild Project
```

### 2. 部署新的jar包到生产环境
```bash
# 停止生产服务
sudo systemctl stop tjuhaitang-miniapp

# 备份当前jar包
cp /www/wwwroot/tjuhaitang_minapp/ruoyi-admin.jar /www/wwwroot/tjuhaitang_minapp/ruoyi-admin.jar.backup

# 上传新的jar包
scp target/ruoyi-admin.jar user@server:/www/wwwroot/tjuhaitang_minapp/

# 启动服务
sudo systemctl start tjuhaitang-miniapp
```

### 3. 验证修复
访问管理后台的项目报名页面，确认：
- ✅ 页面能正常显示项目列表
- ✅ 公司Logo列正常显示
- ✅ 没有审核相关的按钮或状态
- ✅ 用户ID字段正常显示

## 已完成的修改总结

### 数据库修改 ✅
```sql
-- 已删除的字段
ALTER TABLE mini_project_registration 
DROP COLUMN status, 
DROP COLUMN audit_time, 
DROP COLUMN audit_remark;

-- 已添加的字段
ALTER TABLE mini_project_registration 
ADD COLUMN user_id BIGINT NULL COMMENT '用户ID' AFTER id;

-- 已添加的索引
CREATE INDEX idx_mini_project_registration_user_id ON mini_project_registration(user_id);
```

### 后端代码修改 ✅
- **ProjectRegistration.java**: 添加userId字段，删除审核相关字段
- **ProjectRegistrationMapper.xml**: 更新SQL查询，移除审核字段
- **ProjectRegistrationService**: 删除审核方法，添加用户查询方法
- **ProjectRegistrationController**: 删除审核接口，添加用户查询接口

### 前端代码修改 ✅
- **index.vue**: 删除审核对话框和相关JavaScript代码
- **project.js**: 删除审核API调用
- 保留公司Logo显示功能

### API接口验证 ✅
- ✅ `/miniapp/haitang/project/app/register` - 项目报名（支持userId）
- ✅ `/miniapp/haitang/project/app/user/{userId}` - 用户项目查询
- ✅ `/miniapp/haitang/project/list` - 管理端列表（需要认证）

## 注意事项
1. 确保生产环境数据库已经执行了字段删除和添加的SQL语句
2. 重新部署后清理浏览器缓存
3. 验证小程序端功能是否正常
4. 检查日志确认没有其他相关错误

## 联系方式
如果部署过程中遇到问题，请及时联系开发团队。
