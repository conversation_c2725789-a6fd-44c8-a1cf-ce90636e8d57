package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniUserFollow;
import org.apache.ibatis.annotations.Param;

/**
 * 用户关注Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface MiniUserFollowMapper 
{
    /**
     * 查询用户关注
     * 
     * @param followId 用户关注主键
     * @return 用户关注
     */
    public MiniUserFollow selectMiniUserFollowByFollowId(Long followId);

    /**
     * 查询用户关注列表
     * 
     * @param miniUserFollow 用户关注
     * @return 用户关注集合
     */
    public List<MiniUserFollow> selectMiniUserFollowList(MiniUserFollow miniUserFollow);

    /**
     * 新增用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    public int insertMiniUserFollow(MiniUserFollow miniUserFollow);

    /**
     * 修改用户关注
     * 
     * @param miniUserFollow 用户关注
     * @return 结果
     */
    public int updateMiniUserFollow(MiniUserFollow miniUserFollow);

    /**
     * 删除用户关注
     * 
     * @param followId 用户关注主键
     * @return 结果
     */
    public int deleteMiniUserFollowByFollowId(Long followId);

    /**
     * 批量删除用户关注
     * 
     * @param followIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniUserFollowByFollowIds(Long[] followIds);

    /**
     * 查询用户是否已关注某人
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 关注记录
     */
    public MiniUserFollow selectFollowRelation(@Param("followerId") Long followerId, @Param("followedId") Long followedId);

    /**
     * 查询用户的关注列表（我关注的人）
     * 
     * @param followerId 关注者ID
     * @return 关注列表
     */
    public List<MiniUserFollow> selectMyFollowingList(@Param("followerId") Long followerId);

    /**
     * 查询用户的粉丝列表（关注我的人）
     * 
     * @param followedId 被关注者ID
     * @return 粉丝列表
     */
    public List<MiniUserFollow> selectMyFollowersList(@Param("followedId") Long followedId);

    /**
     * 统计用户关注数量
     * 
     * @param userId 用户ID
     * @return 关注数量
     */
    public int countFollowingByUserId(@Param("userId") Long userId);

    /**
     * 统计用户粉丝数量
     * 
     * @param userId 用户ID
     * @return 粉丝数量
     */
    public int countFollowersByUserId(@Param("userId") Long userId);

    /**
     * 更新用户关注状态（关注/取消关注）
     * 
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @param status 状态（0正常 1取消关注）
     * @return 结果
     */
    public int updateFollowStatus(@Param("followerId") Long followerId, 
                                  @Param("followedId") Long followedId, 
                                  @Param("status") String status);

    /**
     * 检查是否互相关注
     * 
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 是否互相关注
     */
    public boolean checkMutualFollow(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    /**
     * 批量查询用户关注状态
     * 
     * @param followerId 关注者ID
     * @param followedIds 被关注者ID列表
     * @return 关注状态列表
     */
    public List<MiniUserFollow> batchSelectFollowStatus(@Param("followerId") Long followerId, 
                                                        @Param("followedIds") List<Long> followedIds);
}
