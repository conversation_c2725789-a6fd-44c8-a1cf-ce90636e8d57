package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.MiniHaitangProjectFormConfigMapper;
import com.ruoyi.miniapp.mapper.MiniHaitangProjectRegistrationMapper;
import com.ruoyi.miniapp.domain.MiniHaitangProjectFormConfig;
import com.ruoyi.miniapp.service.IMiniHaitangProjectFormConfigService;

/**
 * 天大海棠杯项目报名表单配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class MiniHaitangProjectFormConfigServiceImpl implements IMiniHaitangProjectFormConfigService 
{
    @Autowired
    private MiniHaitangProjectFormConfigMapper miniHaitangProjectFormConfigMapper;

    @Autowired
    private MiniHaitangProjectRegistrationMapper miniHaitangProjectRegistrationMapper;

    /**
     * 查询天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 天大海棠杯项目报名表单配置
     */
    @Override
    public MiniHaitangProjectFormConfig selectMiniHaitangProjectFormConfigByConfigId(Long configId)
    {
        return miniHaitangProjectFormConfigMapper.selectMiniHaitangProjectFormConfigByConfigId(configId);
    }

    /**
     * 查询天大海棠杯项目报名表单配置列表
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 天大海棠杯项目报名表单配置
     */
    @Override
    public List<MiniHaitangProjectFormConfig> selectMiniHaitangProjectFormConfigList(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        return miniHaitangProjectFormConfigMapper.selectMiniHaitangProjectFormConfigList(miniHaitangProjectFormConfig);
    }

    /**
     * 新增天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    @Override
    public int insertMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        miniHaitangProjectFormConfig.setCreateTime(DateUtils.getNowDate());
        return miniHaitangProjectFormConfigMapper.insertMiniHaitangProjectFormConfig(miniHaitangProjectFormConfig);
    }

    /**
     * 修改天大海棠杯项目报名表单配置
     * 
     * @param miniHaitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    @Override
    public int updateMiniHaitangProjectFormConfig(MiniHaitangProjectFormConfig miniHaitangProjectFormConfig)
    {
        miniHaitangProjectFormConfig.setUpdateTime(DateUtils.getNowDate());
        return miniHaitangProjectFormConfigMapper.updateMiniHaitangProjectFormConfig(miniHaitangProjectFormConfig);
    }

    /**
     * 批量删除天大海棠杯项目报名表单配置
     * 
     * @param configIds 需要删除的天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniHaitangProjectFormConfigByConfigIds(Long[] configIds)
    {
        // 检查是否有关联的报名记录
        for (Long configId : configIds) {
            int count = miniHaitangProjectRegistrationMapper.countByConfigId(configId);
            if (count > 0) {
                throw new RuntimeException("配置ID为" + configId + "的表单配置存在关联的报名记录，无法删除");
            }
        }
        return miniHaitangProjectFormConfigMapper.deleteMiniHaitangProjectFormConfigByConfigIds(configIds);
    }

    /**
     * 删除天大海棠杯项目报名表单配置信息
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniHaitangProjectFormConfigByConfigId(Long configId)
    {
        // 检查是否有关联的报名记录
        int count = miniHaitangProjectRegistrationMapper.countByConfigId(configId);
        if (count > 0) {
            throw new RuntimeException("该表单配置存在关联的报名记录，无法删除");
        }
        return miniHaitangProjectFormConfigMapper.deleteMiniHaitangProjectFormConfigByConfigId(configId);
    }

    /**
     * 查询启用的表单配置
     * 
     * @return 启用的表单配置
     */
    @Override
    public MiniHaitangProjectFormConfig selectEnabledFormConfig()
    {
        return miniHaitangProjectFormConfigMapper.selectEnabledFormConfig();
    }

    /**
     * 启用表单配置（同时禁用其他配置）
     * 
     * @param configId 配置ID
     * @return 结果
     */
    @Override
    @Transactional
    public int enableFormConfig(Long configId)
    {
        // 先禁用所有配置
        miniHaitangProjectFormConfigMapper.disableAllFormConfigs();
        // 再启用指定配置
        return miniHaitangProjectFormConfigMapper.enableFormConfig(configId);
    }

    /**
     * 查询启用状态的表单配置列表
     * 
     * @return 启用状态的表单配置集合
     */
    @Override
    public List<MiniHaitangProjectFormConfig> selectEnabledMiniHaitangProjectFormConfigList()
    {
        return miniHaitangProjectFormConfigMapper.selectEnabledMiniHaitangProjectFormConfigList();
    }
}
