package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.SensitiveWordCategoryMapper;
import com.ruoyi.miniapp.domain.SensitiveWordCategory;
import com.ruoyi.miniapp.service.ISensitiveWordCategoryService;

/**
 * 敏感词分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SensitiveWordCategoryServiceImpl implements ISensitiveWordCategoryService 
{
    @Autowired
    private SensitiveWordCategoryMapper sensitiveWordCategoryMapper;

    /**
     * 查询敏感词分类
     * 
     * @param categoryId 敏感词分类主键
     * @return 敏感词分类
     */
    @Override
    public SensitiveWordCategory selectSensitiveWordCategoryByCategoryId(Long categoryId)
    {
        return sensitiveWordCategoryMapper.selectSensitiveWordCategoryByCategoryId(categoryId);
    }

    /**
     * 查询敏感词分类列表
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 敏感词分类
     */
    @Override
    public List<SensitiveWordCategory> selectSensitiveWordCategoryList(SensitiveWordCategory sensitiveWordCategory)
    {
        return sensitiveWordCategoryMapper.selectSensitiveWordCategoryList(sensitiveWordCategory);
    }

    /**
     * 查询所有启用的敏感词分类
     * 
     * @return 敏感词分类集合
     */
    @Override
    public List<SensitiveWordCategory> selectEnabledSensitiveWordCategoryList()
    {
        return sensitiveWordCategoryMapper.selectEnabledSensitiveWordCategoryList();
    }

    /**
     * 根据分类编码查询敏感词分类
     * 
     * @param categoryCode 分类编码
     * @return 敏感词分类
     */
    @Override
    public SensitiveWordCategory selectSensitiveWordCategoryByCategoryCode(String categoryCode)
    {
        return sensitiveWordCategoryMapper.selectSensitiveWordCategoryByCategoryCode(categoryCode);
    }

    /**
     * 新增敏感词分类
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 结果
     */
    @Override
    public int insertSensitiveWordCategory(SensitiveWordCategory sensitiveWordCategory)
    {
        sensitiveWordCategory.setCreateTime(DateUtils.getNowDate());
        return sensitiveWordCategoryMapper.insertSensitiveWordCategory(sensitiveWordCategory);
    }

    /**
     * 修改敏感词分类
     * 
     * @param sensitiveWordCategory 敏感词分类
     * @return 结果
     */
    @Override
    public int updateSensitiveWordCategory(SensitiveWordCategory sensitiveWordCategory)
    {
        sensitiveWordCategory.setUpdateTime(DateUtils.getNowDate());
        return sensitiveWordCategoryMapper.updateSensitiveWordCategory(sensitiveWordCategory);
    }

    /**
     * 批量删除敏感词分类
     * 
     * @param categoryIds 需要删除的敏感词分类主键
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordCategoryByCategoryIds(Long[] categoryIds)
    {
        return sensitiveWordCategoryMapper.deleteSensitiveWordCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除敏感词分类信息
     * 
     * @param categoryId 敏感词分类主键
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordCategoryByCategoryId(Long categoryId)
    {
        return sensitiveWordCategoryMapper.deleteSensitiveWordCategoryByCategoryId(categoryId);
    }

    /**
     * 检查分类编码是否唯一
     * 
     * @param categoryCode 分类编码
     * @param categoryId 分类ID（排除自己）
     * @return 结果
     */
    @Override
    public boolean checkCategoryCodeUnique(String categoryCode, Long categoryId)
    {
        int count = sensitiveWordCategoryMapper.checkCategoryCodeUnique(categoryCode, categoryId);
        return count == 0;
    }
}
