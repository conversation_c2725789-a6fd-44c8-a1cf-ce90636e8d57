package com.ruoyi.miniapp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目投资对象 mini_project_investment
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniProjectInvestment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 投资项目ID */
    private Long investmentId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 列表封面图片URL */
    @Excel(name = "封面图片")
    private String coverImageUrl;

    /** 融资轮次 */
    @Excel(name = "融资轮次")
    private String financingRound;

    /** 所属行业ID */
    @Excel(name = "所属行业ID")
    private Long industryId;

    /** 所在地区 */
    @Excel(name = "所在地区")
    private String region;

    /** 项目标签 */
    @Excel(name = "项目标签")
    private String tags;

    /** 项目简介 */
    @Excel(name = "项目简介")
    private String briefIntroduction;

    /** 项目详情（富文本） */
    @Excel(name = "项目详情")
    private String detailContent;

    /** 详情页顶图URL */
    @Excel(name = "顶图")
    private String topImageUrl;

    /** 联系人姓名 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 排序字段 */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 行业名称（关联查询字段，非数据库字段） */
    private String industryName;

    public void setInvestmentId(Long investmentId) 
    {
        this.investmentId = investmentId;
    }

    public Long getInvestmentId() 
    {
        return investmentId;
    }
    
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    
    public void setCoverImageUrl(String coverImageUrl) 
    {
        this.coverImageUrl = coverImageUrl;
    }

    public String getCoverImageUrl() 
    {
        return coverImageUrl;
    }
    
    public void setFinancingRound(String financingRound) 
    {
        this.financingRound = financingRound;
    }

    public String getFinancingRound() 
    {
        return financingRound;
    }
    
    public void setIndustryId(Long industryId) 
    {
        this.industryId = industryId;
    }

    public Long getIndustryId() 
    {
        return industryId;
    }
    
    public void setRegion(String region) 
    {
        this.region = region;
    }

    public String getRegion() 
    {
        return region;
    }
    
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    
    public void setBriefIntroduction(String briefIntroduction) 
    {
        this.briefIntroduction = briefIntroduction;
    }

    public String getBriefIntroduction() 
    {
        return briefIntroduction;
    }
    
    public void setDetailContent(String detailContent) 
    {
        this.detailContent = detailContent;
    }

    public String getDetailContent() 
    {
        return detailContent;
    }
    
    public void setTopImageUrl(String topImageUrl) 
    {
        this.topImageUrl = topImageUrl;
    }

    public String getTopImageUrl() 
    {
        return topImageUrl;
    }
    
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    
    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }
    
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setIndustryName(String industryName) 
    {
        this.industryName = industryName;
    }

    public String getIndustryName() 
    {
        return industryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("investmentId", getInvestmentId())
            .append("projectName", getProjectName())
            .append("coverImageUrl", getCoverImageUrl())
            .append("financingRound", getFinancingRound())
            .append("industryId", getIndustryId())
            .append("region", getRegion())
            .append("tags", getTags())
            .append("briefIntroduction", getBriefIntroduction())
            .append("detailContent", getDetailContent())
            .append("topImageUrl", getTopImageUrl())
            .append("contactPerson", getContactPerson())
            .append("contactInfo", getContactInfo())
            .append("viewCount", getViewCount())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
