<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniEnterpriseMapper">
    
    <resultMap type="MiniEnterprise" id="MiniEnterpriseResult">
        <result property="enterpriseId"    column="enterprise_id"    />
        <result property="enterpriseName"    column="enterprise_name"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="address"    column="address"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="description"    column="description"    />
        <result property="scale"    column="scale"    />
        <result property="foundingDate"    column="founding_date"    />
        <result property="website"    column="website"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.ruoyi.miniapp.domain.MiniEnterprise$MiniEnterpriseIndustry" id="MiniEnterpriseIndustryResult">
        <result property="id"    column="id"    />
        <result property="enterpriseId"    column="enterprise_id"    />
        <result property="industryTreeId"    column="industry_tree_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <association property="industryTree" javaType="com.ruoyi.miniapp.domain.MiniIndustryTree">
            <result property="id"    column="tree_id"    />
            <result property="parentId"    column="tree_parent_id"    />
            <result property="nodeCode"    column="tree_node_code"    />
            <result property="nodeName"    column="tree_node_name"    />
            <result property="nodeDescription"    column="tree_node_description"    />
            <result property="nodeType"    column="tree_node_type"    />
            <result property="nodeLevel"    column="tree_node_level"    />
            <result property="streamType"    column="tree_stream_type"    />
            <result property="sortOrder"    column="tree_sort_order"    />
            <result property="status"    column="tree_status"    />
            <result property="nodePath"    column="tree_node_path"    />
        </association>
    </resultMap>

    <sql id="selectMiniEnterpriseVo">
        select enterprise_id, enterprise_name, legal_person, address, contact_person, contact_phone, contact_email, description, scale, founding_date, website, logo_url, status, create_by, create_time, update_by, update_time, remark from mini_enterprise
    </sql>

    <select id="selectMiniEnterpriseList" parameterType="MiniEnterprise" resultMap="MiniEnterpriseResult">
        <include refid="selectMiniEnterpriseVo"/>
        <where>  
            <if test="enterpriseName != null  and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person like concat('%', #{legalPerson}, '%')</if>
            <if test="scale != null  and scale != ''"> and scale = #{scale}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by enterprise_id desc
    </select>
    
    <select id="selectMiniEnterpriseByEnterpriseId" parameterType="Long" resultMap="MiniEnterpriseResult">
        <include refid="selectMiniEnterpriseVo"/>
        where enterprise_id = #{enterpriseId}
    </select>
        
    <insert id="insertMiniEnterprise" parameterType="MiniEnterprise" useGeneratedKeys="true" keyProperty="enterpriseId">
        insert into mini_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseName != null and enterpriseName != ''">enterprise_name,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="address != null">address,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="description != null">description,</if>
            <if test="scale != null">scale,</if>
            <if test="foundingDate != null">founding_date,</if>
            <if test="website != null">website,</if>
            <if test="logoUrl != null">logo_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseName != null and enterpriseName != ''">#{enterpriseName},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="address != null">#{address},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="description != null">#{description},</if>
            <if test="scale != null">#{scale},</if>
            <if test="foundingDate != null">#{foundingDate},</if>
            <if test="website != null">#{website},</if>
            <if test="logoUrl != null">#{logoUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniEnterprise" parameterType="MiniEnterprise">
        update mini_enterprise
        <trim prefix="SET" suffixOverrides=",">
            <if test="enterpriseName != null and enterpriseName != ''">enterprise_name = #{enterpriseName},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="address != null">address = #{address},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="description != null">description = #{description},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="foundingDate != null">founding_date = #{foundingDate},</if>
            <if test="website != null">website = #{website},</if>
            <if test="logoUrl != null">logo_url = #{logoUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where enterprise_id = #{enterpriseId}
    </update>

    <delete id="deleteMiniEnterpriseByEnterpriseId" parameterType="Long">
        delete from mini_enterprise where enterprise_id = #{enterpriseId}
    </delete>

    <delete id="deleteMiniEnterpriseByEnterpriseIds" parameterType="String">
        delete from mini_enterprise where enterprise_id in 
        <foreach item="enterpriseId" collection="array" open="(" separator="," close=")">
            #{enterpriseId}
        </foreach>
    </delete>

    <!-- 企业产业关联查询 -->
    <select id="selectEnterpriseIndustryByEnterpriseId" parameterType="Long" resultMap="MiniEnterpriseIndustryResult">
        SELECT 
            ei.id,
            ei.enterprise_id,
            ei.industry_tree_id,
            ei.create_time,
            ei.update_time,
            t.id AS tree_id,
            t.parent_id AS tree_parent_id,
            t.node_code AS tree_node_code,
            t.node_name AS tree_node_name,
            t.node_description AS tree_node_description,
            t.node_type AS tree_node_type,
            t.node_level AS tree_node_level,
            t.stream_type AS tree_stream_type,
            t.sort_order AS tree_sort_order,
            t.status AS tree_status,
            t.node_path AS tree_node_path
        FROM mini_enterprise_industry ei
        LEFT JOIN mini_industry_tree t ON ei.industry_tree_id = t.id
        WHERE ei.enterprise_id = #{enterpriseId}
        ORDER BY t.node_level, t.sort_order, t.id
    </select>

    <!-- 删除企业产业关联 -->
    <delete id="deleteMiniEnterpriseIndustryByEnterpriseId" parameterType="Long">
        delete from mini_enterprise_industry where enterprise_id = #{enterpriseId}
    </delete>

    <delete id="deleteMiniEnterpriseIndustryByEnterpriseIds" parameterType="Long">
        delete from mini_enterprise_industry where enterprise_id in 
        <foreach item="enterpriseId" collection="array" open="(" separator="," close=")">
            #{enterpriseId}
        </foreach>
    </delete>

    <!-- 批量插入企业产业关联 -->
    <insert id="batchInsertEnterpriseIndustry" parameterType="java.util.List">
        insert into mini_enterprise_industry (enterprise_id, industry_tree_id, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.enterpriseId}, #{item.industryTreeId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 统计指定行业类型是否被企业绑定 -->
    <select id="countEnterpriseBindIndustryTypes" parameterType="java.util.List" resultType="int">
        SELECT COUNT(1)
        FROM mini_enterprise_industry
        WHERE industry_tree_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>




</mapper> 