import request from '@/utils/request'

// 查询联系人管理列表
export function listContact(query) {
  return request({
    url: '/miniapp/contact/list',
    method: 'post',
    data: query
  })
}

// 查询联系人管理详细
export function getContact(contactId) {
  return request({
    url: '/miniapp/contact/getInfo',
    method: 'post',
    data: contactId
  })
}

// 新增联系人管理
export function addContact(data) {
  return request({
    url: '/miniapp/contact/add',
    method: 'post',
    data: data
  })
}

// 修改联系人管理
export function updateContact(data) {
  return request({
    url: '/miniapp/contact/edit',
    method: 'post',
    data: data
  })
}

// 删除联系人管理
export function delContact(contactIds) {
  // 确保传递的是数组格式
  const ids = Array.isArray(contactIds) ? contactIds : [contactIds];
  return request({
    url: '/miniapp/contact/remove',
    method: 'post',
    data: ids
  })
}

// 更新联系人排序
export function updateContactSort(data) {
  return request({
    url: '/miniapp/contact/updateSort',
    method: 'post',
    data: data
  })
}

// 获取启用的联系人列表（小程序端调用）
export function getEnabledContactList() {
  return request({
    url: '/miniapp/contact/app/getEnabledList',
    method: 'post'
  })
}

// 根据联系人编码获取联系人信息（管理端）
export function getContactByCode(contactCode) {
  return request({
    url: '/miniapp/contact/getByContactCode',
    method: 'post',
    data: contactCode
  })
}

// 根据联系人编码获取联系人信息（小程序端）
export function getContactByCodeForApp(contactCode) {
  return request({
    url: '/miniapp/contact/app/getByContactCode',
    method: 'post',
    data: contactCode
  })
}
