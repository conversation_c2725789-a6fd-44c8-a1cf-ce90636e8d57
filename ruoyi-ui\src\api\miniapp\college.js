import request from '@/utils/request'

// 查询学院信息列表
export function listCollege(query) {
  return request({
    url: '/miniapp/college/list',
    method: 'get',
    params: query
  })
}

// 查询学院信息详细
export function getCollege(collegeId) {
  return request({
    url: '/miniapp/college/' + collegeId,
    method: 'get'
  })
}

// 新增学院信息
export function addCollege(data) {
  return request({
    url: '/miniapp/college',
    method: 'post',
    data: data
  })
}

// 修改学院信息
export function updateCollege(data) {
  return request({
    url: '/miniapp/college',
    method: 'put',
    data: data
  })
}

// 删除学院信息
export function delCollege(collegeId) {
  return request({
    url: '/miniapp/college/' + collegeId,
    method: 'delete'
  })
}

// 获取启用的学院信息列表
export function getEnabledCollegeList() {
  return request({
    url: '/miniapp/college/enabled',
    method: 'get'
  })
}

// 校验学院代码
export function checkCollegeCodeUnique(data) {
  return request({
    url: '/miniapp/college/checkCollegeCodeUnique',
    method: 'post',
    data: data
  })
}
