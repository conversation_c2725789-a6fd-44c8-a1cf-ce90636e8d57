package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 敏感词检测日志对象 sensitive_word_log
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class SensitiveWordLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 模块名称 */
    @Excel(name = "模块名称")
    private String moduleName;

    /** 操作类型（检测/过滤/替换） */
    @Excel(name = "操作类型")
    private String operationType;

    /** 原始内容 */
    @Excel(name = "原始内容")
    private String originalContent;

    /** 过滤后内容 */
    @Excel(name = "过滤后内容")
    private String filteredContent;

    /** 命中的敏感词（JSON格式） */
    @Excel(name = "命中的敏感词")
    private String hitWords;

    /** 命中数量 */
    @Excel(name = "命中数量")
    private Integer hitCount;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String clientIp;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setModuleName(String moduleName) 
    {
        this.moduleName = moduleName;
    }

    public String getModuleName() 
    {
        return moduleName;
    }
    public void setOperationType(String operationType) 
    {
        this.operationType = operationType;
    }

    public String getOperationType() 
    {
        return operationType;
    }
    public void setOriginalContent(String originalContent) 
    {
        this.originalContent = originalContent;
    }

    public String getOriginalContent() 
    {
        return originalContent;
    }
    public void setFilteredContent(String filteredContent) 
    {
        this.filteredContent = filteredContent;
    }

    public String getFilteredContent() 
    {
        return filteredContent;
    }
    public void setHitWords(String hitWords) 
    {
        this.hitWords = hitWords;
    }

    public String getHitWords() 
    {
        return hitWords;
    }
    public void setHitCount(Integer hitCount) 
    {
        this.hitCount = hitCount;
    }

    public Integer getHitCount() 
    {
        return hitCount;
    }
    public void setClientIp(String clientIp) 
    {
        this.clientIp = clientIp;
    }

    public String getClientIp() 
    {
        return clientIp;
    }
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }

    @Override
    public Date getCreateTime() 
    {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("moduleName", getModuleName())
            .append("operationType", getOperationType())
            .append("originalContent", getOriginalContent())
            .append("filteredContent", getFilteredContent())
            .append("hitWords", getHitWords())
            .append("hitCount", getHitCount())
            .append("clientIp", getClientIp())
            .append("userAgent", getUserAgent())
            .append("createTime", getCreateTime())
            .toString();
    }
}
