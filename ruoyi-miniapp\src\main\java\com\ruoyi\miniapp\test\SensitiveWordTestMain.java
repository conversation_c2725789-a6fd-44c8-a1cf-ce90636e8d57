package com.ruoyi.miniapp.test;

import com.github.houbb.sensitive.word.bs.SensitiveWordBs;

import java.util.List;

/**
 * 敏感词测试主类
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class SensitiveWordTestMain {
    
    public static void main(String[] args) {
        System.out.println("=== 敏感词功能测试 ===");
        
        // 初始化敏感词引擎
        SensitiveWordBs sensitiveWordBs = SensitiveWordBs.newInstance()
                .ignoreCase(true)
                .ignoreWidth(true)
                .ignoreNumStyle(true)
                .ignoreChineseStyle(true)
                .ignoreEnglishStyle(true)
                .ignoreRepeat(true)
                .enableNumCheck(true)
                .enableEmailCheck(true)
                .enableUrlCheck(true)
                .init();
        
        System.out.println("敏感词引擎初始化完成");
        
        // 测试文本
        String[] testTexts = {
            "你好，这是一个测试文本。",
            "这个人真是个傻逼",
            "垃圾内容需要过滤",
            "正常的文本内容",
            "包含特殊字符的文本@#$%",
            "数字123和英文abc的混合文本",
            "你这个白痴",
            "死去吧",
            "性感美女",
            "杀死敌人",
            "走私货物",
            "买彩票中奖"
        };
        
        for (String text : testTexts) {
            System.out.println("\n测试文本: " + text);
            
            // 检测是否包含敏感词
            boolean contains = sensitiveWordBs.contains(text);
            System.out.println("是否包含敏感词: " + contains);
            
            if (contains) {
                // 查找所有敏感词
                List<String> sensitiveWords = sensitiveWordBs.findAll(text);
                System.out.println("找到的敏感词: " + sensitiveWords);
                
                // 替换敏感词
                String replacedText = sensitiveWordBs.replace(text);
                System.out.println("替换后文本: " + replacedText);
            }
        }
        
        // 性能测试
        System.out.println("\n=== 性能测试 ===");
        String longText = "这是一个用于性能测试的长文本，包含各种内容，用来测试敏感词检测的性能表现。" +
                         "文本中可能包含一些敏感词汇，需要进行检测和过滤处理。" +
                         "这个测试主要是为了验证在处理较长文本时，敏感词检测功能的响应时间。";
        
        long startTime = System.currentTimeMillis();
        
        // 执行多次检测
        for (int i = 0; i < 1000; i++) {
            sensitiveWordBs.contains(longText);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("1000次敏感词检测耗时: " + duration + "ms");
        System.out.println("平均每次检测耗时: " + (duration / 1000.0) + "ms");
        
        System.out.println("\n=== 测试完成 ===");
    }
}
