{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754299061748}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RGb3JtQ29uZmlnLCBnZXRGb3JtQ29uZmlnLCBkZWxGb3JtQ29uZmlnLCBhZGRGb3JtQ29uZmlnLCB1cGRhdGVGb3JtQ29uZmlnLCBlbmFibGVGb3JtQ29uZmlnIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWciOwppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJGb3JtQ29uZmlnIiwKICBkaWN0czogWydzeXNfbm9ybWFsX2Rpc2FibGUnXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7ooajmoLzmlbDmja4KICAgICAgZm9ybUNvbmZpZ0xpc3Q6IFtdLAogICAgICAvLyDlvZPliY3lkK/nlKjnmoTphY3nva4KICAgICAgZW5hYmxlZENvbmZpZzogbnVsbCwKICAgICAgLy8g5ZCv55So6YWN572u55qE6KGo5Y2V5a2X5q61CiAgICAgIGVuYWJsZWRGb3JtRmllbGRzOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrooajljZXphY3nva7lvLnlh7rlsYIKICAgICAgZm9ybUNvbmZpZ09wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrooajljZXpooTop4jlvLnlh7rlsYIKICAgICAgcHJldmlld0RpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICAvLyDlvZPliY3phY3nva7nmoTooajljZXlrZfmrrUKICAgICAgY3VycmVudEZvcm1GaWVsZHM6IFtdLAogICAgICAvLyDlvZPliY3mk43kvZznmoTphY3nva4KICAgICAgY3VycmVudENvbmZpZzogbnVsbCwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY29uZmlnTmFtZTogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBjb25maWdOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6YWN572u5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L295ZCv55So55qE6YWN572uICovCiAgICBsb2FkRW5hYmxlZENvbmZpZygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgcmVxdWVzdCh7CiAgICAgICAgdXJsOiAnL21pbmlhcHAvaGFpdGFuZy9mb3JtQ29uZmlnL2VuYWJsZWQnLAogICAgICAgIG1ldGhvZDogJ2dldCcKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgIHRoaXMuZW5hYmxlZENvbmZpZyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICB0aGlzLmxvYWRFbmFibGVkRm9ybUZpZWxkcygpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmVuYWJsZWRDb25maWcgPSBudWxsOwogICAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IFtdOwogICAgICAgIH0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuZW5hYmxlZENvbmZpZyA9IG51bGw7CiAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IFtdOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yqg6L295ZCv55So6YWN572u55qE6KGo5Y2V5a2X5q61ICovCiAgICBsb2FkRW5hYmxlZEZvcm1GaWVsZHMoKSB7CiAgICAgIGlmICh0aGlzLmVuYWJsZWRDb25maWcgJiYgdGhpcy5lbmFibGVkQ29uZmlnLmZvcm1Db25maWcpIHsKICAgICAgICB0cnkgewogICAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IEpTT04ucGFyc2UodGhpcy5lbmFibGVkQ29uZmlnLmZvcm1Db25maWcpOwogICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgIHRoaXMuZW5hYmxlZEZvcm1GaWVsZHMgPSBbXTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IFtdOwogICAgICB9CiAgICB9LAogICAgLyoqIOafpeivouWkqeWkp+a1t+ajoOadr+mhueebruaKpeWQjeihqOWNlemFjee9ruWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RGb3JtQ29uZmlnKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybUNvbmZpZ0xpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBjb25maWdJZDogbnVsbCwKICAgICAgICBjb25maWdOYW1lOiBudWxsLAogICAgICAgIGNvbmZpZ0Rlc2NyaXB0aW9uOiBudWxsLAogICAgICAgIGZvcm1Db25maWc6IG51bGwsCiAgICAgICAgaXNFbmFibGVkOiAiMCIsCiAgICAgICAgc29ydE9yZGVyOiAwLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgdXBkYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29uZmlnSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6KGo5Y2V6YWN572uIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgY29uZmlnSWQgPSByb3cuY29uZmlnSWQgfHwgdGhpcy5pZHMKICAgICAgZ2V0Rm9ybUNvbmZpZyhjb25maWdJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6KGo5Y2V6YWN572uIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uY29uZmlnSWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVGb3JtQ29uZmlnKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRm9ybUNvbmZpZyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgY29uZmlnSWRzID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7nvJblj7fkuLoiJyArIGNvbmZpZ0lkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsRm9ybUNvbmZpZyhjb25maWdJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDooajljZXphY3nva7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUZvcm1Db25maWcocm93KSB7CiAgICAgIHRoaXMuY3VycmVudENvbmZpZyA9IHJvdzsKICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IFtdOwogICAgICBpZiAocm93LmZvcm1Db25maWcpIHsKICAgICAgICB0cnkgewogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IEpTT04ucGFyc2Uocm93LmZvcm1Db25maWcpOwogICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgIHRoaXMuY3VycmVudEZvcm1GaWVsZHMgPSBbXTsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOmihOiniOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUHJldmlldyhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q29uZmlnID0gcm93OwogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107CiAgICAgIGlmIChyb3cuZm9ybUNvbmZpZykgewogICAgICAgIHRyeSB7CiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gSlNPTi5wYXJzZShyb3cuZm9ybUNvbmZpZyk7CiAgICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IFtdOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnByZXZpZXdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5ZCv55So5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFbmFibGUocm93KSB7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+WQr+eUqOatpOmFjee9ruWwhuemgeeUqOWFtuS7luaJgOaciemFjee9ru+8jOaYr+WQpuehruiupOWQr+eUqO+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGVuYWJsZUZvcm1Db25maWcocm93LmNvbmZpZ0lkKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOwogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWQr+eUqOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOa3u+WKoOihqOWNleWtl+autSAqLwogICAgYWRkRm9ybUZpZWxkKCkgewogICAgICBjb25zdCBkZWZhdWx0TmFtZSA9IHRoaXMuZ2VuZXJhdGVVbmlxdWVGaWVsZE5hbWUoJ2ZpZWxkJyk7CiAgICAgIHRoaXMuY3VycmVudEZvcm1GaWVsZHMucHVzaCh7CiAgICAgICAgbmFtZTogZGVmYXVsdE5hbWUsCiAgICAgICAgbGFiZWw6ICcnLAogICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICAgIG9wdGlvbnM6ICcnCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTooajljZXlrZfmrrUgKi8KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgewogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLnNwbGljZShpbmRleCwgMSk7CiAgICB9LAogICAgLyoqIOeUn+aIkOWUr+S4gOWtl+auteWQjSAqLwogICAgZ2VuZXJhdGVVbmlxdWVGaWVsZE5hbWUocHJlZml4KSB7CiAgICAgIGxldCBjb3VudGVyID0gMTsKICAgICAgbGV0IG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOwogICAgICB3aGlsZSAodGhpcy5jdXJyZW50Rm9ybUZpZWxkcy5zb21lKGZpZWxkID0+IGZpZWxkLm5hbWUgPT09IG5hbWUpKSB7CiAgICAgICAgY291bnRlcisrOwogICAgICAgIG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOwogICAgICB9CiAgICAgIHJldHVybiBuYW1lOwogICAgfSwKICAgIC8qKiDliKTmlq3lrZfmrrXnsbvlnovmmK/lkKbpnIDopoHpgInpobkgKi8KICAgIG5lZWRPcHRpb25zKHR5cGUpIHsKICAgICAgcmV0dXJuIFsncmFkaW8nLCAnY2hlY2tib3gnLCAnc2VsZWN0JywgJ3JhZGlvX290aGVyJywgJ2NoZWNrYm94X290aGVyJywgJ3NlbGVjdF9vdGhlcicsICdyYXRlJ10uaW5jbHVkZXModHlwZSk7CiAgICB9LAogICAgLyoqIOWkhOeQhuaooeadv+WRveS7pCAqLwogICAgaGFuZGxlVGVtcGxhdGVDb21tYW5kKGNvbW1hbmQpIHsKICAgICAgaWYgKGNvbW1hbmQgPT09ICdjbGVhcicpIHsKICAgICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTmuIXnqbrmiYDmnInlrZfmrrXvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuY3VycmVudEZvcm1GaWVsZHMgPSBbXTsKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNvbnN0IHRlbXBsYXRlcyA9IHsKICAgICAgICBiYXNpYzogWwogICAgICAgICAgeyBsYWJlbDogJ+Wnk+WQjScsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAnJywgdHlwZTogJ3RlbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICcnLCB0eXBlOiAnZW1haWwnLCByZXF1aXJlZDogZmFsc2UsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6Lqr5Lu96K+B5Y+3JywgbmFtZTogJycsIHR5cGU6ICdpZGNhcmQnLCByZXF1aXJlZDogZmFsc2UsIG9wdGlvbnM6ICcnIH0KICAgICAgICBdLAogICAgICAgIHByb2plY3Q6IFsKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67lkI3np7AnLCBuYW1lOiAnJywgdHlwZTogJ2lucHV0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6LSf6LSj5Lq6JywgbmFtZTogJycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+iBlOezu+eUteivnScsIG5hbWU6ICcnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6YKu566x5Zyw5Z2AJywgbmFtZTogJycsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+mhueebrueugOS7iycsIG5hbWU6ICcnLCB0eXBlOiAndGV4dGFyZWEnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67nsbvlnosnLCBuYW1lOiAnJywgdHlwZTogJ3NlbGVjdF9vdGhlcicsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAn56eR5oqA5Yib5pawLOWVhuS4muaooeW8jyznpL7kvJrlhaznm4os5paH5YyW5Yib5oSPJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+WboumYn+inhOaooScsIG5hbWU6ICcnLCB0eXBlOiAncmFkaW8nLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJzHkurosMi0z5Lq6LDQtNeS6uiw2LTEw5Lq6LDEw5Lq65Lul5LiKJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+mhueebrumYtuautScsIG5hbWU6ICcnLCB0eXBlOiAnc2VsZWN0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICfliJvmhI/pmLbmrrUs5Yid5Yib6Zi25q61LOaIkOmVv+mYtuautSzmiJDnhp/pmLbmrrUnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6aKE5pyf5oqV6LWE6YeR6aKdJywgbmFtZTogJycsIHR5cGU6ICdtb25leScsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67nvZHnq5knLCBuYW1lOiAnJywgdHlwZTogJ3VybCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67or4TliIYnLCBuYW1lOiAnJywgdHlwZTogJ3JhdGUnLCByZXF1aXJlZDogZmFsc2UsIG9wdGlvbnM6ICcxLDIsMyw0LDUnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5piv5ZCm5ZCM5oSP5p2h5qy+JywgbmFtZTogJycsIHR5cGU6ICdzd2l0Y2gnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67orqHliJLkuaYnLCBuYW1lOiAnJywgdHlwZTogJ2ZpbGUnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfmvJTnpLrop4bpopEnLCBuYW1lOiAnJywgdHlwZTogJ2ZpbGUnLCByZXF1aXJlZDogZmFsc2UsIG9wdGlvbnM6ICcnIH0KICAgICAgICBdCiAgICAgIH07CgogICAgICBpZiAodGVtcGxhdGVzW2NvbW1hbmRdKSB7CiAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IHRlbXBsYXRlc1tjb21tYW5kXS5tYXAoZmllbGQgPT4gKHsKICAgICAgICAgIC4uLmZpZWxkLAogICAgICAgICAgbmFtZTogdGhpcy5nZW5lcmF0ZVVuaXF1ZUZpZWxkTmFtZShmaWVsZC5sYWJlbC50b0xvd2VyQ2FzZSgpKQogICAgICAgIH0pKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDpooTop4jooajljZUgKi8KICAgIGhhbmRsZVByZXZpZXdGb3JtKCkgewogICAgICB0aGlzLnByZXZpZXdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5L+d5a2Y6KGo5Y2V6YWN572uICovCiAgICBzYXZlRm9ybUNvbmZpZygpIHsKICAgICAgaWYgKCF0aGlzLmN1cnJlbnRDb25maWcpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35YWI6YCJ5oup6KaB6YWN572u55qE6KGo5Y2VIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDpqozor4HlrZfmrrXphY3nva4KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgZmllbGQgPSB0aGlzLmN1cnJlbnRGb3JtRmllbGRzW2ldOwogICAgICAgIGlmICghZmllbGQubGFiZWwpIHsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBpZiAoIWZpZWxkLm5hbWUpIHsKICAgICAgICAgIGZpZWxkLm5hbWUgPSB0aGlzLmdlbmVyYXRlVW5pcXVlRmllbGROYW1lKGZpZWxkLmxhYmVsLnRvTG93ZXJDYXNlKCkpOwogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5uZWVkT3B0aW9ucyhmaWVsZC50eXBlKSAmJiAhZmllbGQub3B0aW9ucykgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOesrCR7aSArIDF95Liq5a2X5q61IiR7ZmllbGQubGFiZWx9IumcgOimgeiuvue9rumAiemhueWGheWuuWApOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQoKICAgICAgY29uc3QgZm9ybURhdGEgPSB7CiAgICAgICAgY29uZmlnSWQ6IHRoaXMuY3VycmVudENvbmZpZy5jb25maWdJZCwKICAgICAgICBmb3JtQ29uZmlnOiBKU09OLnN0cmluZ2lmeSh0aGlzLmN1cnJlbnRGb3JtRmllbGRzKQogICAgICB9OwoKICAgICAgdXBkYXRlRm9ybUNvbmZpZyhmb3JtRGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7CiAgICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IGZhbHNlOwogICAgICAgIHRoaXMubG9hZEVuYWJsZWRDb25maWcoKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPluWtl+autemAiemhuSAqLwogICAgZ2V0RmllbGRPcHRpb25zKG9wdGlvbnMpIHsKICAgICAgaWYgKCFvcHRpb25zKSByZXR1cm4gW107CiAgICAgIHJldHVybiBvcHRpb25zLnNwbGl0KCcsJykubWFwKG9wdCA9PiBvcHQudHJpbSgpKS5maWx0ZXIob3B0ID0+IG9wdCk7CiAgICB9LAogICAgLyoqIOiOt+WPlumAiemhuei+k+WFpeahhuWNoOS9jeespiAqLwogICAgZ2V0T3B0aW9uc1BsYWNlaG9sZGVyKHR5cGUpIHsKICAgICAgY29uc3QgcGxhY2Vob2xkZXJzID0gewogICAgICAgIHJhZGlvOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTMnLAogICAgICAgIGNoZWNrYm94OiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTMnLAogICAgICAgIHNlbGVjdDogJ+mAiemhueWGheWuue+8jOWkmuS4qumAiemhueeUqOmAl+WPt+WIhumalO+8jOWmgu+8mumAiemhuTEs6YCJ6aG5MizpgInpobkzJywKICAgICAgICByYWRpb19vdGhlcjogJ+mAiemhueWGheWuue+8jOWkmuS4qumAiemhueeUqOmAl+WPt+WIhumalO+8jOWmgu+8mumAiemhuTEs6YCJ6aG5MizpgInpobkz77yI5Lya6Ieq5Yqo5re75YqgIuWFtuS7liLpgInpobnvvIknLAogICAgICAgIGNoZWNrYm94X290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsCiAgICAgICAgc2VsZWN0X290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsCiAgICAgICAgcmF0ZTogJ+ivhOWIhuetiee6p++8jOeUqOmAl+WPt+WIhumalO+8jOWmgu+8mjEsMiwzLDQsNSDmiJYg5beuLOS4gOiIrCzoia/lpb0s5LyY56eALOWNk+i2iicKICAgICAgfTsKICAgICAgcmV0dXJuIHBsYWNlaG9sZGVyc1t0eXBlXSB8fCAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqUJzsKICAgIH0sCiAgICAvKiog6I635Y+W5a2X5q615Zu+5qCHICovCiAgICBnZXRGaWVsZEljb24odHlwZSkgewogICAgICBjb25zdCBpY29ucyA9IHsKICAgICAgICBpbnB1dDogJ2VsLWljb24tZWRpdCcsCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywKICAgICAgICBudW1iZXI6ICdlbC1pY29uLXMtZGF0YScsCiAgICAgICAgZW1haWw6ICdlbC1pY29uLW1lc3NhZ2UnLAogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLAogICAgICAgIHJhZGlvOiAnZWwtaWNvbi1jaXJjbGUtY2hlY2snLAogICAgICAgIGNoZWNrYm94OiAnZWwtaWNvbi1jaGVjaycsCiAgICAgICAgc2VsZWN0OiAnZWwtaWNvbi1hcnJvdy1kb3duJywKICAgICAgICByYWRpb19vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsCiAgICAgICAgY2hlY2tib3hfb3RoZXI6ICdlbC1pY29uLWNpcmNsZS1wbHVzLW91dGxpbmUnLAogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsCiAgICAgICAgZGF0ZTogJ2VsLWljb24tZGF0ZScsCiAgICAgICAgdGltZTogJ2VsLWljb24tdGltZScsCiAgICAgICAgZGF0ZXRpbWU6ICdlbC1pY29uLWRhdGUnLAogICAgICAgIGZpbGU6ICdlbC1pY29uLXVwbG9hZCcsCiAgICAgICAgdXJsOiAnZWwtaWNvbi1saW5rJywKICAgICAgICBpZGNhcmQ6ICdlbC1pY29uLXBvc3RjYXJkJywKICAgICAgICBtb25leTogJ2VsLWljb24tY29pbicsCiAgICAgICAgcmF0ZTogJ2VsLWljb24tc3Rhci1vbicsCiAgICAgICAgc2xpZGVyOiAnZWwtaWNvbi1zb3J0JywKICAgICAgICBzd2l0Y2g6ICdlbC1pY29uLXN3aXRjaC1idXR0b24nLAogICAgICAgIGNvbG9yOiAnZWwtaWNvbi1icnVzaCcKICAgICAgfTsKICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOwogICAgfSwKICAgIC8qKiDojrflj5blrZfmrrXnsbvlnovlkI3np7AgKi8KICAgIGdldEZpZWxkVHlwZU5hbWUodHlwZSkgewogICAgICBjb25zdCBuYW1lcyA9IHsKICAgICAgICBpbnB1dDogJ+aWh+acrOi+k+WFpScsCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLAogICAgICAgIG51bWJlcjogJ+aVsOWtl+i+k+WFpScsCiAgICAgICAgZW1haWw6ICfpgq7nrrEnLAogICAgICAgIHRlbDogJ+eUteivnScsCiAgICAgICAgcmFkaW86ICfljZXpgIknLAogICAgICAgIGNoZWNrYm94OiAn5aSa6YCJJywKICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLAogICAgICAgIHJhZGlvX290aGVyOiAn5Y2V6YCJK+WFtuS7licsCiAgICAgICAgY2hlY2tib3hfb3RoZXI6ICflpJrpgIkr5YW25LuWJywKICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywKICAgICAgICBkYXRlOiAn5pel5pyfJywKICAgICAgICB0aW1lOiAn5pe26Ze0JywKICAgICAgICBkYXRldGltZTogJ+aXpeacn+aXtumXtCcsCiAgICAgICAgZmlsZTogJ+aWh+S7tuS4iuS8oCcsCiAgICAgICAgdXJsOiAn572R5Z2A6ZO+5o6lJywKICAgICAgICBpZGNhcmQ6ICfouqvku73or4Hlj7cnLAogICAgICAgIG1vbmV5OiAn6YeR6aKdJywKICAgICAgICByYXRlOiAn6K+E5YiGJywKICAgICAgICBzbGlkZXI6ICfmu5HlnZcnLAogICAgICAgIHN3aXRjaDogJ+W8gOWFsycsCiAgICAgICAgY29sb3I6ICfpopzoibLpgInmi6knCiAgICAgIH07CiAgICAgIHJldHVybiBuYW1lc1t0eXBlXSB8fCAn5pyq55+l57G75Z6LJzsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaGFpdGFuZy9mb3JtQ29uZmlnL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGBmb3JtQ29uZmlnXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2b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file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectFormConfig", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 表单配置管理区域 -->\n    <div class=\"form-config-container\">\n      <div class=\"config-header\">\n        <h3>项目报名表单配置管理</h3>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd\"\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\n          >新增配置</el-button>\n        </div>\n      </div>\n\n      <div class=\"config-content\" v-loading=\"loading\">\n        <!-- 当前启用的配置 -->\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\n          <div class=\"enabled-header\">\n            <h4>\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\n              当前启用配置：{{ enabledConfig.configName }}\n            </h4>\n            <div class=\"enabled-actions\">\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                icon=\"el-icon-setting\"\n                @click=\"handleFormConfig(enabledConfig)\"\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              >配置表单</el-button>\n              <el-button\n                size=\"small\"\n                type=\"success\"\n                icon=\"el-icon-view\"\n                @click=\"handlePreview(enabledConfig)\"\n              >预览表单</el-button>\n            </div>\n          </div>\n          <div class=\"enabled-description\">\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\n          </div>\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\n            <h5>表单字段预览：</h5>\n            <div class=\"field-list\">\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\n                <div class=\"field-info\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                  <span class=\"field-label\">{{ field.label }}</span>\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div v-else class=\"empty-form\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\n          </div>\n        </div>\n\n        <!-- 无启用配置时的提示 -->\n        <div v-else class=\"no-enabled-config\">\n          <i class=\"el-icon-warning-outline\"></i>\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 所有配置列表 -->\n    <div class=\"config-list-container\">\n      <div class=\"list-header\">\n        <h4>所有表单配置</h4>\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\n          <el-form-item label=\"配置名称\" prop=\"configName\">\n            <el-input\n              v-model=\"queryParams.configName\"\n              placeholder=\"请输入配置名称\"\n              clearable\n              style=\"width: 200px;\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\n              <el-option\n                v-for=\"dict in dict.type.sys_normal_disable\"\n                :key=\"dict.value\"\n                :label=\"dict.label\"\n                :value=\"dict.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\n            <el-tag v-else type=\"info\">未启用</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >配置</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit\"\n              @click=\"handleUpdate(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n            >编辑</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-check\"\n              @click=\"handleEnable(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\n              v-if=\"scope.row.isEnabled !== '1'\"\n            >启用</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-delete\"\n              @click=\"handleDelete(scope.row)\"\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </div>\n\n    <!-- 添加或修改配置基本信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"配置名称\" prop=\"configName\">\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\n        </el-form-item>\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单配置对话框 -->\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\n      <div class=\"form-fields-config\">\n        <!-- 工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\n              <el-button size=\"small\">\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"advanced\">高级字段模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\n          </div>\n        </div>\n\n        <!-- 字段配置列表 -->\n        <div class=\"form-fields-list\">\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\n            <i class=\"el-icon-document-add\"></i>\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\n          </div>\n          <div v-else>\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\n              <div class=\"field-config-header\">\n                <span class=\"field-index\">{{ index + 1 }}</span>\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\">\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\n                  <el-option label=\"📞 电话\" value=\"tel\" />\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                  <el-option label=\"📅 日期\" value=\"date\" />\n                  <el-option label=\"⏰ 时间\" value=\"time\" />\n                  <el-option label=\"📅⏰ 日期时间\" value=\"datetime\" />\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\n                  <el-option label=\"🔗 网址链接\" value=\"url\" />\n                  <el-option label=\"🏢 身份证号\" value=\"idcard\" />\n                  <el-option label=\"💰 金额\" value=\"money\" />\n                  <el-option label=\"📏 评分\" value=\"rate\" />\n                  <el-option label=\"🎚️ 滑块\" value=\"slider\" />\n                  <el-option label=\"🔄 开关\" value=\"switch\" />\n                  <el-option label=\"🌈 颜色选择\" value=\"color\" />\n                </el-select>\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\n              </div>\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\n                <el-input\n                  v-model=\"field.options\"\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\n                  size=\"small\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <!-- 基础输入类型 -->\n              <el-input\n                v-if=\"['input', 'email', 'tel', 'url', 'idcard'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 数字类型 -->\n              <el-input-number\n                v-else-if=\"['number', 'money'].includes(field.type)\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 单选类型 -->\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-radio>\n              </el-radio-group>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\n                  <el-radio\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-radio>\n                  <el-radio label=\"其他\">其他</el-radio>\n                </el-radio-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 多选类型 -->\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                >{{ option }}</el-checkbox>\n              </el-checkbox-group>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\n                  <el-checkbox\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                  >{{ option }}</el-checkbox>\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\n                </el-checkbox-group>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 下拉选择 -->\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\n                <el-option\n                  v-for=\"option in getFieldOptions(field.options)\"\n                  :key=\"option\"\n                  :label=\"option\"\n                  :value=\"option\"\n                />\n              </el-select>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\n                  <el-option\n                    v-for=\"option in getFieldOptions(field.options)\"\n                    :key=\"option\"\n                    :label=\"option\"\n                    :value=\"option\"\n                  />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\n              </div>\n              <!-- 日期时间类型 -->\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-time-picker\n                v-else-if=\"field.type === 'time'\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-date-picker\n                v-else-if=\"field.type === 'datetime'\"\n                type=\"datetime\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <!-- 评分 -->\n              <el-rate\n                v-else-if=\"field.type === 'rate'\"\n                disabled\n                :max=\"getFieldOptions(field.options).length || 5\"\n              />\n              <!-- 滑块 -->\n              <el-slider\n                v-else-if=\"field.type === 'slider'\"\n                disabled\n                :max=\"100\"\n                style=\"margin: 12px 0;\"\n              />\n              <!-- 开关 -->\n              <el-switch\n                v-else-if=\"field.type === 'switch'\"\n                disabled\n              />\n              <!-- 颜色选择 -->\n              <el-color-picker\n                v-else-if=\"field.type === 'color'\"\n                disabled\n              />\n              <!-- 文件上传 -->\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :auto-upload=\"false\"\n                disabled\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\n              </el-upload>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig } from \"@/api/miniapp/haitang/formConfig\";\nimport request from '@/utils/request';\n\nexport default {\n  name: \"FormConfig\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      listLoading: false,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 天大海棠杯项目报名表单配置表格数据\n      formConfigList: [],\n      // 当前启用的配置\n      enabledConfig: null,\n      // 启用配置的表单字段\n      enabledFormFields: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 当前配置的表单字段\n      currentFormFields: [],\n      // 当前操作的配置\n      currentConfig: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        configName: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        configName: [\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadEnabledConfig();\n    this.getList();\n  },\n  methods: {\n    /** 加载启用的配置 */\n    loadEnabledConfig() {\n      this.loading = true;\n      request({\n        url: '/miniapp/haitang/formConfig/enabled',\n        method: 'get'\n      }).then(response => {\n        if (response.data) {\n          this.enabledConfig = response.data;\n          this.loadEnabledFormFields();\n        } else {\n          this.enabledConfig = null;\n          this.enabledFormFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.enabledConfig = null;\n        this.enabledFormFields = [];\n        this.loading = false;\n      });\n    },\n    /** 加载启用配置的表单字段 */\n    loadEnabledFormFields() {\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\n        try {\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\n        } catch (e) {\n          this.enabledFormFields = [];\n        }\n      } else {\n        this.enabledFormFields = [];\n      }\n    },\n    /** 查询天大海棠杯项目报名表单配置列表 */\n    getList() {\n      this.listLoading = true;\n      listFormConfig(this.queryParams).then(response => {\n        this.formConfigList = response.rows;\n        this.total = response.total;\n        this.listLoading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        configId: null,\n        configName: null,\n        configDescription: null,\n        formConfig: null,\n        isEnabled: \"0\",\n        sortOrder: 0,\n        status: \"0\",\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.configId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加天大海棠杯项目报名表单配置\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const configId = row.configId || this.ids\n      getFormConfig(configId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改天大海棠杯项目报名表单配置\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.configId != null) {\n            updateFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addFormConfig(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const configIds = row.configId || this.ids;\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\n        return delFormConfig(configIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.formConfigOpen = true;\n    },\n    /** 预览按钮操作 */\n    handlePreview(row) {\n      this.currentConfig = row;\n      this.currentFormFields = [];\n      if (row.formConfig) {\n        try {\n          this.currentFormFields = JSON.parse(row.formConfig);\n        } catch (e) {\n          this.currentFormFields = [];\n        }\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 启用按钮操作 */\n    handleEnable(row) {\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\n        return enableFormConfig(row.configId);\n      }).then(() => {\n        this.loadEnabledConfig();\n        this.getList();\n        this.$modal.msgSuccess(\"启用成功\");\n      }).catch(() => {});\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      const defaultName = this.generateUniqueFieldName('field');\n      this.currentFormFields.push({\n        name: defaultName,\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.currentFormFields.splice(index, 1);\n    },\n    /** 生成唯一字段名 */\n    generateUniqueFieldName(prefix) {\n      let counter = 1;\n      let name = prefix + counter;\n      while (this.currentFormFields.some(field => field.name === name)) {\n        counter++;\n        name = prefix + counter;\n      }\n      return name;\n    },\n    /** 判断字段类型是否需要选项 */\n    needOptions(type) {\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'rate'].includes(type);\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$modal.confirm('确认清空所有字段？').then(() => {\n          this.currentFormFields = [];\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' },\n          { label: '身份证号', name: '', type: 'idcard', required: false, options: '' }\n        ],\n        project: [\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '项目阶段', name: '', type: 'select', required: true, options: '创意阶段,初创阶段,成长阶段,成熟阶段' },\n          { label: '预期投资金额', name: '', type: 'money', required: false, options: '' },\n          { label: '项目网站', name: '', type: 'url', required: false, options: '' },\n          { label: '项目评分', name: '', type: 'rate', required: false, options: '1,2,3,4,5' },\n          { label: '是否同意条款', name: '', type: 'switch', required: true, options: '' },\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.currentFormFields = templates[command].map(field => ({\n          ...field,\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\n        }));\n      }\n    },\n    /** 预览表单 */\n    handlePreviewForm() {\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (!this.currentConfig) {\n        this.$modal.msgError(\"请先选择要配置的表单\");\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.currentFormFields.length; i++) {\n        const field = this.currentFormFields[i];\n        if (!field.label) {\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (!field.name) {\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\n        }\n        if (this.needOptions(field.type) && !field.options) {\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\n          return;\n        }\n      }\n\n      const formData = {\n        configId: this.currentConfig.configId,\n        formConfig: JSON.stringify(this.currentFormFields)\n      };\n\n      updateFormConfig(formData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadEnabledConfig();\n        this.getList();\n      });\n    },\n    /** 获取字段选项 */\n    getFieldOptions(options) {\n      if (!options) return [];\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\n    },\n    /** 获取选项输入框占位符 */\n    getOptionsPlaceholder(type) {\n      const placeholders = {\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\n        rate: '评分等级，用逗号分隔，如：1,2,3,4,5 或 差,一般,良好,优秀,卓越'\n      };\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-circle-check',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus-outline',\n        checkbox_other: 'el-icon-circle-plus-outline',\n        select_other: 'el-icon-circle-plus-outline',\n        date: 'el-icon-date',\n        time: 'el-icon-time',\n        datetime: 'el-icon-date',\n        file: 'el-icon-upload',\n        url: 'el-icon-link',\n        idcard: 'el-icon-postcard',\n        money: 'el-icon-coin',\n        rate: 'el-icon-star-on',\n        slider: 'el-icon-sort',\n        switch: 'el-icon-switch-button',\n        color: 'el-icon-brush'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const names = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        time: '时间',\n        datetime: '日期时间',\n        file: '文件上传',\n        url: '网址链接',\n        idcard: '身份证号',\n        money: '金额',\n        rate: '评分',\n        slider: '滑块',\n        switch: '开关',\n        color: '颜色选择'\n      };\n      return names[type] || '未知类型';\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/haitang/formConfig/export', {\n        ...this.queryParams\n      }, `formConfig_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n\n<style scoped>\n.form-config-container {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 200px;\n}\n\n.enabled-config {\n  border: 1px solid #67c23a;\n  border-radius: 6px;\n  padding: 16px;\n  background: #f0f9ff;\n}\n\n.enabled-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.enabled-header h4 {\n  margin: 0;\n  color: #67c23a;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.enabled-description {\n  margin-bottom: 16px;\n  color: #606266;\n}\n\n.form-preview h5 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n}\n\n.empty-form, .no-enabled-config {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-form i, .no-enabled-config i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.config-list-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.list-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.form-fields-config {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.form-fields-list {\n  padding: 16px;\n}\n\n.empty-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n}\n\n.empty-fields i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.field-config-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.field-config-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.field-index {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  color: white;\n  border-radius: 50%;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.field-config-body {\n  padding: 12px 16px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.preview-header h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 4px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"]}]}