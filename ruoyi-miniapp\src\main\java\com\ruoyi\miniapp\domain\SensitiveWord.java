package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 敏感词对象 sensitive_word
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class SensitiveWord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 敏感词ID */
    private Long wordId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String categoryName;

    /** 敏感词内容 */
    @Excel(name = "敏感词内容")
    private String wordContent;

    /** 词类型（1敏感词 2白名单） */
    @Excel(name = "词类型", readConverterExp = "1=敏感词,2=白名单")
    private String wordType;

    /** 严重程度（1轻微 2中等 3严重） */
    @Excel(name = "严重程度", readConverterExp = "1=轻微,2=中等,3=严重")
    private String severityLevel;

    /** 替换字符 */
    @Excel(name = "替换字符")
    private String replacementChar;

    /** 是否正则表达式（0否 1是） */
    @Excel(name = "是否正则表达式", readConverterExp = "0=否,1=是")
    private String isRegex;

    /** 命中次数 */
    @Excel(name = "命中次数")
    private Long hitCount;

    /** 最后命中时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后命中时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastHitTime;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setWordId(Long wordId) 
    {
        this.wordId = wordId;
    }

    public Long getWordId() 
    {
        return wordId;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }
    public void setWordContent(String wordContent) 
    {
        this.wordContent = wordContent;
    }

    public String getWordContent() 
    {
        return wordContent;
    }
    public void setWordType(String wordType) 
    {
        this.wordType = wordType;
    }

    public String getWordType() 
    {
        return wordType;
    }
    public void setSeverityLevel(String severityLevel) 
    {
        this.severityLevel = severityLevel;
    }

    public String getSeverityLevel() 
    {
        return severityLevel;
    }
    public void setReplacementChar(String replacementChar) 
    {
        this.replacementChar = replacementChar;
    }

    public String getReplacementChar() 
    {
        return replacementChar;
    }
    public void setIsRegex(String isRegex) 
    {
        this.isRegex = isRegex;
    }

    public String getIsRegex() 
    {
        return isRegex;
    }
    public void setHitCount(Long hitCount) 
    {
        this.hitCount = hitCount;
    }

    public Long getHitCount() 
    {
        return hitCount;
    }
    public void setLastHitTime(Date lastHitTime) 
    {
        this.lastHitTime = lastHitTime;
    }

    public Date getLastHitTime() 
    {
        return lastHitTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("wordId", getWordId())
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("wordContent", getWordContent())
            .append("wordType", getWordType())
            .append("severityLevel", getSeverityLevel())
            .append("replacementChar", getReplacementChar())
            .append("isRegex", getIsRegex())
            .append("hitCount", getHitCount())
            .append("lastHitTime", getLastHitTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
