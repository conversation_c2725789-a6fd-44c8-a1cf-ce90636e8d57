import request from '@/utils/request'

// 查询项目投资列表
export function listInvestment(query) {
  return request({
    url: '/miniapp/investment/list',
    method: 'get',
    params: query
  })
}

// 查询项目投资详细
export function getInvestment(investmentId) {
  return request({
    url: '/miniapp/investment/' + investmentId,
    method: 'get'
  })
}

// 新增项目投资
export function addInvestment(data) {
  return request({
    url: '/miniapp/investment',
    method: 'post',
    data: data
  })
}

// 修改项目投资
export function updateInvestment(data) {
  return request({
    url: '/miniapp/investment',
    method: 'put',
    data: data
  })
}

// 删除项目投资
export function delInvestment(investmentId) {
  return request({
    url: '/miniapp/investment/' + investmentId,
    method: 'delete'
  })
}

// 查询启用的项目投资列表（小程序端调用）
export function listEnabledInvestment(params) {
  return request({
    url: '/miniapp/investment/enabled',
    method: 'get',
    params: params
  })
}

// 查询推荐的项目投资列表
export function listRecommendedInvestment() {
  return request({
    url: '/miniapp/investment/recommended',
    method: 'get'
  })
}

// 增加项目投资浏览次数
export function incrementViewCount(investmentId) {
  return request({
    url: '/miniapp/investment/view/' + investmentId,
    method: 'post'
  })
}

// 根据行业ID查询项目投资列表
export function listInvestmentByIndustry(industryId) {
  return request({
    url: '/miniapp/investment/industry/' + industryId,
    method: 'get'
  })
}

// 根据融资轮次查询项目投资列表
export function listInvestmentByFinancing(financingRound) {
  return request({
    url: '/miniapp/investment/financing/' + financingRound,
    method: 'get'
  })
}

// 根据地区查询项目投资列表
export function listInvestmentByRegion(region) {
  return request({
    url: '/miniapp/investment/region/' + region,
    method: 'get'
  })
}

// 查询行业树列表
export function listIndustryTree() {
  return request({
    url: '/miniapp/industry/tree',
    method: 'get'
  })
}
