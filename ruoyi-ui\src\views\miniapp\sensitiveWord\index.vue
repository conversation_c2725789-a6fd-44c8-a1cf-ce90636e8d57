<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.categoryId"
            :label="category.categoryName"
            :value="category.categoryId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="敏感词" prop="wordContent">
        <el-input
          v-model="queryParams.wordContent"
          placeholder="请输入敏感词内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="词类型" prop="wordType">
        <el-select v-model="queryParams.wordType" placeholder="请选择词类型" clearable>
          <el-option label="敏感词" value="1" />
          <el-option label="白名单" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="严重程度" prop="severityLevel">
        <el-select v-model="queryParams.severityLevel" placeholder="请选择严重程度" clearable>
          <el-option label="轻微" value="1" />
          <el-option label="中等" value="2" />
          <el-option label="严重" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:sensitiveWord:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:sensitiveWord:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:sensitiveWord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:sensitiveWord:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefreshCache"
          v-hasPermi="['miniapp:sensitiveWord:refresh']"
        >刷新缓存</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-search"
          size="mini"
          @click="handleCheckDialog"
        >敏感词检测</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="敏感词ID" align="center" prop="wordId" />
      <el-table-column label="分类" align="center" prop="categoryName" />
      <el-table-column label="敏感词内容" align="center" prop="wordContent" />
      <el-table-column label="词类型" align="center" prop="wordType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.wordType === '1' ? 'danger' : 'success'">
            {{ scope.row.wordType === '1' ? '敏感词' : '白名单' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="严重程度" align="center" prop="severityLevel">
        <template slot-scope="scope">
          <el-tag :type="getSeverityType(scope.row.severityLevel)">
            {{ getSeverityText(scope.row.severityLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="替换字符" align="center" prop="replacementChar" />
      <el-table-column label="命中次数" align="center" prop="hitCount" />
      <el-table-column label="最后命中时间" align="center" prop="lastHitTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastHitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:sensitiveWord:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:sensitiveWord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改敏感词对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择分类">
            <el-option
              v-for="category in categoryList"
              :key="category.categoryId"
              :label="category.categoryName"
              :value="category.categoryId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="敏感词内容" prop="wordContent">
          <el-input v-model="form.wordContent" placeholder="请输入敏感词内容" />
        </el-form-item>
        <el-form-item label="词类型" prop="wordType">
          <el-radio-group v-model="form.wordType">
            <el-radio label="1">敏感词</el-radio>
            <el-radio label="2">白名单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="严重程度" prop="severityLevel">
          <el-radio-group v-model="form.severityLevel">
            <el-radio label="1">轻微</el-radio>
            <el-radio label="2">中等</el-radio>
            <el-radio label="3">严重</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="替换字符" prop="replacementChar">
          <el-input v-model="form.replacementChar" placeholder="请输入替换字符" maxlength="10" />
        </el-form-item>
        <el-form-item label="正则表达式" prop="isRegex">
          <el-radio-group v-model="form.isRegex">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 敏感词检测对话框 -->
    <el-dialog title="敏感词检测" :visible.sync="checkOpen" width="600px" append-to-body>
      <el-form ref="checkForm" :model="checkForm" label-width="80px">
        <el-form-item label="检测文本">
          <el-input v-model="checkForm.text" type="textarea" :rows="4" placeholder="请输入要检测的文本" />
        </el-form-item>
        <el-form-item label="替换字符">
          <el-input v-model="checkForm.replacement" placeholder="请输入替换字符，默认为*" maxlength="1" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCheck">检测敏感词</el-button>
          <el-button type="success" @click="handleFilter">过滤敏感词</el-button>
        </el-form-item>
        <el-form-item label="检测结果" v-if="checkResult.contains !== null">
          <el-tag :type="checkResult.contains ? 'danger' : 'success'">
            {{ checkResult.contains ? '包含敏感词' : '未发现敏感词' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="命中敏感词" v-if="checkResult.hitWords && checkResult.hitWords.length > 0">
          <el-tag v-for="word in checkResult.hitWords" :key="word" type="danger" style="margin-right: 5px;">
            {{ word }}
          </el-tag>
        </el-form-item>
        <el-form-item label="过滤结果" v-if="checkResult.filteredText">
          <el-input v-model="checkResult.filteredText" type="textarea" :rows="4" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="checkOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSensitiveWord, getSensitiveWord, delSensitiveWord, addSensitiveWord, updateSensitiveWord, checkSensitiveWord, filterSensitiveWord, refreshSensitiveWordCache, getEnabledSensitiveWordCategoryList } from "@/api/miniapp/sensitiveWord";

export default {
  name: "SensitiveWord",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 敏感词表格数据
      wordList: [],
      // 分类列表
      categoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示检测对话框
      checkOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: null,
        wordContent: null,
        wordType: null,
        severityLevel: null,
        status: null
      },
      // 表单参数
      form: {},
      // 检测表单参数
      checkForm: {
        text: '',
        replacement: '*'
      },
      // 检测结果
      checkResult: {
        contains: null,
        hitWords: [],
        filteredText: ''
      },
      // 表单校验
      rules: {
        wordContent: [
          { required: true, message: "敏感词内容不能为空", trigger: "blur" }
        ],
        wordType: [
          { required: true, message: "词类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCategoryList();
  },
  methods: {
    /** 查询敏感词列表 */
    getList() {
      this.loading = true;
      listSensitiveWord(this.queryParams).then(response => {
        this.wordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询分类列表 */
    getCategoryList() {
      getEnabledSensitiveWordCategoryList().then(response => {
        this.categoryList = response.data;
      });
    },
    // 获取严重程度类型
    getSeverityType(level) {
      const typeMap = {
        '1': 'info',
        '2': 'warning',
        '3': 'danger'
      };
      return typeMap[level] || 'info';
    },
    // 获取严重程度文本
    getSeverityText(level) {
      const textMap = {
        '1': '轻微',
        '2': '中等',
        '3': '严重'
      };
      return textMap[level] || '未知';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        wordId: null,
        categoryId: null,
        wordContent: null,
        wordType: "1",
        severityLevel: "1",
        replacementChar: "*",
        isRegex: "0",
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.wordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加敏感词";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const wordId = row.wordId || this.ids
      getSensitiveWord(wordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改敏感词";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.wordId != null) {
            updateSensitiveWord(this.form).then(() => {
              this.$message({
                type: 'success',
                message: '修改成功!'
              });
              this.open = false;
              this.getList();
            });
          } else {
            addSensitiveWord(this.form).then(() => {
              this.$message({
                type: 'success',
                message: '新增成功!'
              });
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const wordIds = row.wordId || this.ids;
      this.$confirm('是否确认删除敏感词编号为"' + wordIds + '"的数据项？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        return delSensitiveWord(wordIds);
      }).then(() => {
        this.getList();
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/sensitiveWord/export', {
        ...this.queryParams
      }, `sensitiveWord_${new Date().getTime()}.xlsx`)
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshSensitiveWordCache().then(() => {
        this.$message({
          type: 'success',
          message: '缓存刷新成功!'
        });
      });
    },
    /** 敏感词检测对话框 */
    handleCheckDialog() {
      this.checkOpen = true;
      this.checkResult = {
        contains: null,
        hitWords: [],
        filteredText: ''
      };
    },
    /** 检测敏感词 */
    handleCheck() {
      if (!this.checkForm.text) {
        this.$message.warning('请输入要检测的文本');
        return;
      }
      checkSensitiveWord(this.checkForm.text).then(response => {
        // 后端使用AjaxResult.put()方法，数据直接在response根级别，不在data下
        this.checkResult.contains = response.contains;
        this.checkResult.hitWords = response.hitWords || [];
        console.log('检测结果:', response); // 添加调试日志
      }).catch(error => {
        console.error('检测敏感词失败:', error);
        this.$message.error('检测敏感词失败');
      });
    },
    /** 过滤敏感词 */
    handleFilter() {
      if (!this.checkForm.text) {
        this.$message.warning('请输入要过滤的文本');
        return;
      }
      filterSensitiveWord(this.checkForm.text, this.checkForm.replacement).then(response => {
        // 后端使用AjaxResult.put()方法，数据直接在response根级别，不在data下
        this.checkResult.filteredText = response.filteredText;
        this.checkResult.hitWords = response.hitWords || [];
        console.log('过滤结果:', response); // 添加调试日志
      }).catch(error => {
        console.error('过滤敏感词失败:', error);
        this.$message.error('过滤敏感词失败');
      });
    }
  }
};
</script>
