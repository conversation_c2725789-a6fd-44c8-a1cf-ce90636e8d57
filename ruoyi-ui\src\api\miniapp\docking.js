import request from '@/utils/request'

// 查询需求对接列表
export function listDocking(query) {
  return request({
    url: '/miniapp/docking/list',
    method: 'get',
    params: query
  })
}

// 查询需求对接详细
export function getDocking(dockingId) {
  return request({
    url: '/miniapp/docking/' + dockingId,
    method: 'get'
  })
}

// 新增需求对接
export function addDocking(data) {
  return request({
    url: '/miniapp/docking',
    method: 'post',
    data: data
  })
}

// 修改需求对接
export function updateDocking(data) {
  return request({
    url: '/miniapp/docking',
    method: 'put',
    data: data
  })
}

// 删除需求对接
export function delDocking(dockingId) {
  return request({
    url: '/miniapp/docking/' + dockingId,
    method: 'delete'
  })
}

// 导出需求对接
export function exportDocking(query) {
  return request({
    url: '/miniapp/docking/export',
    method: 'post',
    params: query
  })
}

// 获取需求的对接详情列表
export function getDockingDetailListByDemandId(demandId) {
  return request({
    url: '/miniapp/docking/demand/' + demandId,
    method: 'get'
  })
}

// 获取用户的对接记录列表
export function getUserDockingList(userId) {
  return request({
    url: '/miniapp/docking/user/' + userId,
    method: 'get'
  })
}

// 更新联系状态
export function updateContactStatus(data) {
  return request({
    url: '/miniapp/docking/updateContactStatus',
    method: 'put',
    data: data
  })
}
