package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.SensitiveWord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 敏感词Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Mapper
public interface SensitiveWordMapper 
{
    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词主键
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordByWordId(Long wordId);

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord);

    /**
     * 查询所有启用的敏感词
     * 
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectEnabledSensitiveWordList();

    /**
     * 根据词类型查询敏感词
     * 
     * @param wordType 词类型（1敏感词 2白名单）
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordByType(String wordType);

    /**
     * 根据分类ID查询敏感词
     * 
     * @param categoryId 分类ID
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordByCategoryId(Long categoryId);

    /**
     * 根据敏感词内容查询
     * 
     * @param wordContent 敏感词内容
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordByContent(String wordContent);

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int insertSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int updateSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 删除敏感词
     * 
     * @param wordId 敏感词主键
     * @return 结果
     */
    public int deleteSensitiveWordByWordId(Long wordId);

    /**
     * 批量删除敏感词
     *
     * @param wordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSensitiveWordByWordIds(Long[] wordIds);

    /**
     * 根据ID数组查询敏感词列表
     *
     * @param wordIds 敏感词ID数组
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordByWordIds(Long[] wordIds);

    /**
     * 更新敏感词命中次数
     * 
     * @param wordContent 敏感词内容
     * @return 结果
     */
    public int updateSensitiveWordHitCount(String wordContent);

    /**
     * 批量更新敏感词命中次数
     * 
     * @param wordContents 敏感词内容列表
     * @return 结果
     */
    public int batchUpdateSensitiveWordHitCount(@Param("wordContents") List<String> wordContents);

    /**
     * 检查敏感词内容是否唯一
     * 
     * @param wordContent 敏感词内容
     * @param wordId 敏感词ID（排除自己）
     * @return 结果
     */
    public int checkWordContentUnique(@Param("wordContent") String wordContent, @Param("wordId") Long wordId);
}
