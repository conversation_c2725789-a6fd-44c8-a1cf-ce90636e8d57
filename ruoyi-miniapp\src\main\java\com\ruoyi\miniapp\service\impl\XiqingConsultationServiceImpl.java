package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.XiqingConsultationMapper;
import com.ruoyi.miniapp.domain.XiqingConsultation;
import com.ruoyi.miniapp.service.IXiqingConsultationService;

/**
 * 西青金种子报名咨询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class XiqingConsultationServiceImpl implements IXiqingConsultationService 
{
    @Autowired
    private XiqingConsultationMapper xiqingConsultationMapper;

    /**
     * 查询西青金种子报名咨询
     * 
     * @param consultationId 西青金种子报名咨询主键
     * @return 西青金种子报名咨询
     */
    @Override
    public XiqingConsultation selectXiqingConsultationByConsultationId(Long consultationId)
    {
        return xiqingConsultationMapper.selectXiqingConsultationByConsultationId(consultationId);
    }

    /**
     * 查询西青金种子报名咨询列表
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 西青金种子报名咨询
     */
    @Override
    public List<XiqingConsultation> selectXiqingConsultationList(XiqingConsultation xiqingConsultation)
    {
        return xiqingConsultationMapper.selectXiqingConsultationList(xiqingConsultation);
    }

    /**
     * 新增西青金种子报名咨询
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 结果
     */
    @Override
    public int insertXiqingConsultation(XiqingConsultation xiqingConsultation)
    {
        xiqingConsultation.setCreateTime(DateUtils.getNowDate());
        return xiqingConsultationMapper.insertXiqingConsultation(xiqingConsultation);
    }

    /**
     * 修改西青金种子报名咨询
     * 
     * @param xiqingConsultation 西青金种子报名咨询
     * @return 结果
     */
    @Override
    public int updateXiqingConsultation(XiqingConsultation xiqingConsultation)
    {
        xiqingConsultation.setUpdateTime(DateUtils.getNowDate());
        return xiqingConsultationMapper.updateXiqingConsultation(xiqingConsultation);
    }

    /**
     * 批量删除西青金种子报名咨询
     * 
     * @param consultationIds 需要删除的西青金种子报名咨询主键
     * @return 结果
     */
    @Override
    public int deleteXiqingConsultationByConsultationIds(Long[] consultationIds)
    {
        return xiqingConsultationMapper.deleteXiqingConsultationByConsultationIds(consultationIds);
    }

    /**
     * 删除西青金种子报名咨询信息
     * 
     * @param consultationId 西青金种子报名咨询主键
     * @return 结果
     */
    @Override
    public int deleteXiqingConsultationByConsultationId(Long consultationId)
    {
        return xiqingConsultationMapper.deleteXiqingConsultationByConsultationId(consultationId);
    }
}
