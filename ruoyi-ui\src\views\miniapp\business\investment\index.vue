<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="融资轮次" prop="financingRound">
        <el-select v-model="queryParams.financingRound" placeholder="请选择融资轮次" clearable>
          <el-option label="种子轮" value="种子轮" />
          <el-option label="天使轮" value="天使轮" />
          <el-option label="Pre-A轮" value="Pre-A轮" />
          <el-option label="A轮" value="A轮" />
          <el-option label="A+轮" value="A+轮" />
          <el-option label="B轮" value="B轮" />
          <el-option label="B+轮" value="B+轮" />
          <el-option label="C轮" value="C轮" />
          <el-option label="C+轮" value="C+轮" />
          <el-option label="D轮" value="D轮" />
          <el-option label="E轮" value="E轮" />
          <el-option label="F轮" value="F轮" />
          <el-option label="IPO" value="IPO" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区" prop="region">
        <el-select v-model="queryParams.region" placeholder="请选择所在地区" clearable filterable>
          <el-option
            v-for="province in provinceOptions"
            :key="province.value"
            :label="province.label"
            :value="province.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:investment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:investment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:investment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:investment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="investmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目ID" align="center" prop="investmentId" width="80" />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" show-overflow-tooltip />
      <el-table-column label="封面图片" align="center" prop="coverImageUrl" width="120">
        <template slot-scope="scope">
          <image-preview :src="scope.row.coverImageUrl" :width="80" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="融资轮次" align="center" prop="financingRound" width="110">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.financingRound }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属行业" align="center" prop="industryName" min-width="120" show-overflow-tooltip />
      <el-table-column label="所在地区" align="center" prop="region" width="100" />
      <el-table-column label="项目标签" align="center" prop="tags" min-width="250" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag
            v-for="(tag, index) in getTagArray(scope.row.tags)"
            :key="index"
            size="mini"
            style="margin-right: 5px; margin-bottom: 3px;"
            type="info"
          >
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="briefIntroduction" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.briefIntroduction }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactPerson" width="100" />
      <el-table-column label="浏览次数" align="center" prop="viewCount" width="90" />
      <el-table-column label="排序" align="center" prop="sortOrder" width="70" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:investment:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:investment:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:investment:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目投资对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="融资轮次" prop="financingRound">
              <el-select v-model="form.financingRound" placeholder="请选择融资轮次">
                <el-option label="种子轮" value="种子轮" />
                <el-option label="天使轮" value="天使轮" />
                <el-option label="Pre-A轮" value="Pre-A轮" />
                <el-option label="A轮" value="A轮" />
                <el-option label="A+轮" value="A+轮" />
                <el-option label="B轮" value="B轮" />
                <el-option label="B+轮" value="B+轮" />
                <el-option label="C轮" value="C轮" />
                <el-option label="C+轮" value="C+轮" />
                <el-option label="D轮" value="D轮" />
                <el-option label="E轮" value="E轮" />
                <el-option label="F轮" value="F轮" />
                <el-option label="IPO" value="IPO" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属行业" prop="industryId">
              <el-select v-model="form.industryId" placeholder="请选择所属行业" filterable>
                <el-option
                  v-for="industry in industryOptions"
                  :key="industry.id"
                  :label="industry.nodeName"
                  :value="industry.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在地区" prop="region">
              <el-select v-model="form.region" placeholder="请选择所在地区" filterable>
                <el-option
                  v-for="province in provinceOptions"
                  :key="province.value"
                  :label="province.label"
                  :value="province.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactInfo">
              <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="项目标签" prop="tags">
          <el-input v-model="form.tags" placeholder="请输入项目标签，多个标签用逗号分隔" />
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            示例：人工智能,大数据,云计算,物联网
          </div>
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImageUrl">
          <image-upload v-model="form.coverImageUrl"/>
        </el-form-item>
        <el-form-item label="详情顶图" prop="topImageUrl">
          <image-upload v-model="form.topImageUrl"/>
        </el-form-item>
        <el-form-item label="项目简介" prop="briefIntroduction">
          <el-input v-model="form.briefIntroduction" type="textarea" :rows="3" placeholder="请输入项目简介" />
        </el-form-item>
        <el-form-item label="项目详情" prop="detailContent">
          <editor v-model="form.detailContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInvestment, getInvestment, delInvestment, addInvestment, updateInvestment } from "@/api/miniapp/investment";
import { listIndustryTree } from "@/api/miniapp/investment";

export default {
  name: "Investment",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目投资表格数据
      investmentList: [],
      // 行业树选项
      industryOptions: [],
      // 省份选项 - 直接使用简称
      provinceOptions: [
        { value: '北京', label: '北京' },
        { value: '天津', label: '天津' },
        { value: '河北', label: '河北' },
        { value: '山西', label: '山西' },
        { value: '内蒙古', label: '内蒙古' },
        { value: '辽宁', label: '辽宁' },
        { value: '吉林', label: '吉林' },
        { value: '黑龙江', label: '黑龙江' },
        { value: '上海', label: '上海' },
        { value: '江苏', label: '江苏' },
        { value: '浙江', label: '浙江' },
        { value: '安徽', label: '安徽' },
        { value: '福建', label: '福建' },
        { value: '江西', label: '江西' },
        { value: '山东', label: '山东' },
        { value: '河南', label: '河南' },
        { value: '湖北', label: '湖北' },
        { value: '湖南', label: '湖南' },
        { value: '广东', label: '广东' },
        { value: '广西', label: '广西' },
        { value: '海南', label: '海南' },
        { value: '重庆', label: '重庆' },
        { value: '四川', label: '四川' },
        { value: '贵州', label: '贵州' },
        { value: '云南', label: '云南' },
        { value: '西藏', label: '西藏' },
        { value: '陕西', label: '陕西' },
        { value: '甘肃', label: '甘肃' },
        { value: '青海', label: '青海' },
        { value: '宁夏', label: '宁夏' },
        { value: '新疆', label: '新疆' },
        { value: '台湾', label: '台湾' },
        { value: '香港', label: '香港' },
        { value: '澳门', label: '澳门' }
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        financingRound: null,
        industryId: null,
        region: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        financingRound: [
          { required: true, message: "融资轮次不能为空", trigger: "change" }
        ],
        industryId: [
          { required: true, message: "所属行业不能为空", trigger: "change" }
        ],
        region: [
          { required: true, message: "所在地区不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getIndustryTree();
  },
  methods: {
    /** 查询项目投资列表 */
    getList() {
      this.loading = true;
      listInvestment(this.queryParams).then(response => {
        this.investmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询行业树 */
    getIndustryTree() {
      listIndustryTree().then(response => {
        this.industryOptions = response.data;
      });
    },
    /** 处理标签数组 */
    getTagArray(tags) {
      if (!tags) return [];
      return tags.split(',').filter(tag => tag.trim() !== '');
    },
    /** 处理地区名称，去掉省市自治区等后缀 */
    processRegionName(regionName) {
      if (!regionName) return regionName;

      // 定义需要去掉的后缀
      const suffixes = ['省', '市', '自治区', '特别行政区', '壮族自治区', '回族自治区', '维吾尔自治区'];

      let processedName = regionName;
      for (const suffix of suffixes) {
        if (processedName.endsWith(suffix)) {
          processedName = processedName.substring(0, processedName.length - suffix.length);
          break; // 只去掉第一个匹配的后缀
        }
      }

      return processedName;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        investmentId: null,
        projectName: null,
        coverImageUrl: null,
        financingRound: null,
        industryId: null,
        region: null,
        tags: null,
        briefIntroduction: null,
        detailContent: null,
        topImageUrl: null,
        contactPerson: null,
        contactInfo: null,
        viewCount: 0,
        sortOrder: 0,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.investmentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目投资";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const investmentId = row.investmentId || this.ids
      getInvestment(investmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看项目投资详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const investmentId = row.investmentId || this.ids
      getInvestment(investmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目投资";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.investmentId != null) {
            updateInvestment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInvestment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const investmentIds = row.investmentId || this.ids;
      this.$modal.confirm('是否确认删除项目投资编号为"' + investmentIds + '"的数据项？').then(function() {
        return delInvestment(investmentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/investment/export', {
        ...this.queryParams
      }, `investment_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
