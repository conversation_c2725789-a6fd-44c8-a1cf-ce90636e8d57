package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.SensitiveWordCategory;
import com.ruoyi.miniapp.service.ISensitiveWordCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 敏感词分类Controller
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/miniapp/sensitiveWordCategory")
public class SensitiveWordCategoryController extends BaseController
{
    @Autowired
    private ISensitiveWordCategoryService sensitiveWordCategoryService;

    /**
     * 查询敏感词分类列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:list')")
    @GetMapping("/list")
    public TableDataInfo list(SensitiveWordCategory sensitiveWordCategory)
    {
        startPage();
        List<SensitiveWordCategory> list = sensitiveWordCategoryService.selectSensitiveWordCategoryList(sensitiveWordCategory);
        return getDataTable(list);
    }

    /**
     * 查询所有启用的敏感词分类
     */
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<SensitiveWordCategory> list = sensitiveWordCategoryService.selectEnabledSensitiveWordCategoryList();
        return AjaxResult.success(list);
    }

    /**
     * 导出敏感词分类列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:export')")
    @Log(title = "敏感词分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SensitiveWordCategory sensitiveWordCategory)
    {
        List<SensitiveWordCategory> list = sensitiveWordCategoryService.selectSensitiveWordCategoryList(sensitiveWordCategory);
        ExcelUtil<SensitiveWordCategory> util = new ExcelUtil<SensitiveWordCategory>(SensitiveWordCategory.class);
        util.exportExcel(response, list, "敏感词分类数据");
    }

    /**
     * 获取敏感词分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return AjaxResult.success(sensitiveWordCategoryService.selectSensitiveWordCategoryByCategoryId(categoryId));
    }

    /**
     * 新增敏感词分类
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:add')")
    @Log(title = "敏感词分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SensitiveWordCategory sensitiveWordCategory)
    {
        // 检查分类编码是否唯一
        if (!sensitiveWordCategoryService.checkCategoryCodeUnique(sensitiveWordCategory.getCategoryCode(), null))
        {
            return AjaxResult.error("新增敏感词分类'" + sensitiveWordCategory.getCategoryName() + "'失败，分类编码已存在");
        }
        sensitiveWordCategory.setCreateBy(getUsername());
        return toAjax(sensitiveWordCategoryService.insertSensitiveWordCategory(sensitiveWordCategory));
    }

    /**
     * 修改敏感词分类
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:edit')")
    @Log(title = "敏感词分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SensitiveWordCategory sensitiveWordCategory)
    {
        // 检查分类编码是否唯一
        if (!sensitiveWordCategoryService.checkCategoryCodeUnique(sensitiveWordCategory.getCategoryCode(), sensitiveWordCategory.getCategoryId()))
        {
            return AjaxResult.error("修改敏感词分类'" + sensitiveWordCategory.getCategoryName() + "'失败，分类编码已存在");
        }
        sensitiveWordCategory.setUpdateBy(getUsername());
        return toAjax(sensitiveWordCategoryService.updateSensitiveWordCategory(sensitiveWordCategory));
    }

    /**
     * 删除敏感词分类
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordCategory:remove')")
    @Log(title = "敏感词分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(sensitiveWordCategoryService.deleteSensitiveWordCategoryByCategoryIds(categoryIds));
    }
}
